<template>
  <el-dialog
    v-model="visible"
    :title="`${moduleName} - 变更日志记录`"
    width="90%"
    top="5vh"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="query-form">
      <el-form-item label="操作类型">
        <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable>
          <el-option label="新增" value="INSERT" />
          <el-option label="修改" value="UPDATE" />
          <el-option label="删除" value="DELETE" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getChangeLogList">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 变更日志列表 -->
    <el-table 
      :data="changeLogList" 
      border 
      v-loading="loading"
      @row-click="handleRowClick"
      style="cursor: pointer;"
    >
      <el-table-column prop="operationType" label="操作类型" width="100">
        <template #default="scope">
          <el-tag 
            :type="getOperationTypeTag(scope.row.operationType)"
            size="small"
          >
            {{ getOperationTypeName(scope.row.operationType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="tableName" label="表名" width="200" />
      <el-table-column prop="recordId" label="记录ID" width="100" />
      <el-table-column prop="operatorName" label="操作人" width="120" />
      <el-table-column prop="createTime" label="操作时间" width="180" />
      <el-table-column prop="remark" label="备注" />
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button 
            type="primary" 
            size="small" 
            @click.stop="viewChangeDetail(scope.row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="getChangeLogList"
        @current-change="getChangeLogList"
      />
    </div>
  </el-dialog>

  <!-- 变更详情对话框 -->
  <el-dialog
    v-model="detailVisible"
    title="变更详情对比"
    width="80%"
    top="5vh"
  >
    <div class="change-detail">
      <!-- 字段对比表格 -->
      <div class="field-comparison">
        <el-table :data="comparisonData" border style="width: 100%">
          <el-table-column prop="fieldName" label="字段名称" width="200" />
          <el-table-column label="变更前" width="300">
            <template #default="scope">
              <span v-if="scope.row.oldValue !== null" class="field-value">{{ scope.row.oldValue }}</span>
              <span v-else class="no-data">-</span>
            </template>
          </el-table-column>
          <el-table-column label="变更后" width="300">
            <template #default="scope">
              <span v-if="scope.row.newValue !== null" class="field-value">{{ scope.row.newValue }}</span>
              <span v-else class="no-data">-</span>
            </template>
          </el-table-column>
          <el-table-column label="变更状态" width="120">
            <template #default="scope">
              <el-tag
                :type="getChangeStatusType(scope.row.changeType)"
                size="small"
              >
                {{ getChangeStatusText(scope.row.changeType) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 如果没有字段变更，显示提示 -->
        <div v-if="comparisonData.length === 0" class="no-comparison-data">
          <el-empty description="无字段变更数据" />
        </div>
      </div>
      
      <!-- 操作信息 -->
      <div class="operation-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeTag(currentDetail.operationType)">
              {{ getOperationTypeName(currentDetail.operationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人">{{ currentDetail.operatorName || '系统' }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ currentDetail.createTime }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ currentDetail.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { listChangeLogByFacility, listChangeLogByRecord } from '@/api/wastewater/changeLog'
import { ElMessage } from 'element-plus'
import { getFieldDisplayName, formatFieldValue } from './FieldMappings.js'

// Props
interface Props {
  modelValue: boolean
  facilityName?: string
  tableName?: string
  recordId?: string
  moduleName?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  facilityName: '',
  tableName: '',
  recordId: '',
  moduleName: '模块'
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const detailVisible = ref(false)
const loading = ref(false)
const changeLogList = ref([])
const total = ref(0)
const dateRange = ref([])
const currentDetail = ref({})

// 字段对比数据
const comparisonData = computed(() => {
  if (!currentDetail.value.tableName) {
    return []
  }

  const oldData = parseJsonSafely(currentDetail.value.oldValues)
  const newData = parseJsonSafely(currentDetail.value.newValues)
  const tableName = currentDetail.value.tableName

  // 获取所有字段
  const allFields = new Set()
  if (oldData) Object.keys(oldData).forEach(key => allFields.add(key))
  if (newData) Object.keys(newData).forEach(key => allFields.add(key))

  const comparison = []
  allFields.forEach(field => {
    // 跳过一些不需要显示的字段
    if (['id', 'createTime', 'updateTime', 'creator', 'modifier'].includes(field)) {
      return
    }

    const oldValue = oldData ? oldData[field] : null
    const newValue = newData ? newData[field] : null

    // 判断变更类型
    let changeType = 'unchanged'
    if (oldValue === null && newValue !== null) {
      changeType = 'added'
    } else if (oldValue !== null && newValue === null) {
      changeType = 'removed'
    } else if (oldValue !== newValue) {
      changeType = 'modified'
    }

    // 只显示有变更的字段
    if (changeType !== 'unchanged') {
      comparison.push({
        fieldName: getFieldDisplayName(tableName, field),
        fieldKey: field,
        oldValue: formatFieldValue(field, oldValue),
        newValue: formatFieldValue(field, newValue),
        changeType: changeType
      })
    }
  })

  return comparison
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  operationType: '',
  beginTime: '',
  endTime: ''
})

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetQuery()
    getChangeLogList()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取变更日志列表
const getChangeLogList = async () => {
  try {
    loading.value = true
    
    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.beginTime = dateRange.value[0]
      queryParams.endTime = dateRange.value[1]
    } else {
      queryParams.beginTime = ''
      queryParams.endTime = ''
    }

    let response
    if (props.tableName && props.recordId) {
      // 根据表名和记录ID查询
      response = await listChangeLogByRecord(props.tableName, props.recordId, queryParams)
    } else if (props.facilityName) {
      // 根据设施名称查询
      response = await listChangeLogByFacility(props.facilityName, queryParams)
    } else {
      ElMessage.warning('缺少查询条件')
      return
    }

    changeLogList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取变更日志失败:', error)
    ElMessage.error('获取变更日志失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  queryParams.pageNum = 1
  queryParams.pageSize = 20
  queryParams.operationType = ''
  queryParams.beginTime = ''
  queryParams.endTime = ''
  dateRange.value = []
}

// 查看变更详情
const viewChangeDetail = (row: any) => {
  currentDetail.value = row
  detailVisible.value = true
}

// 处理行点击
const handleRowClick = (row: any) => {
  viewChangeDetail(row)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  detailVisible.value = false
}

// 获取操作类型标签样式
const getOperationTypeTag = (type: string) => {
  switch (type) {
    case 'INSERT': return 'success'
    case 'UPDATE': return 'warning'
    case 'DELETE': return 'danger'
    default: return 'info'
  }
}

// 获取操作类型名称
const getOperationTypeName = (type: string) => {
  switch (type) {
    case 'INSERT': return '新增'
    case 'UPDATE': return '修改'
    case 'DELETE': return '删除'
    default: return type
  }
}

// 安全解析JSON
const parseJsonSafely = (jsonStr: string) => {
  if (!jsonStr) return null
  try {
    return JSON.parse(jsonStr)
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return null
  }
}

// 获取变更状态类型
const getChangeStatusType = (changeType: string) => {
  switch (changeType) {
    case 'added': return 'success'
    case 'removed': return 'danger'
    case 'modified': return 'warning'
    default: return 'info'
  }
}

// 获取变更状态文本
const getChangeStatusText = (changeType: string) => {
  switch (changeType) {
    case 'added': return '新增'
    case 'removed': return '删除'
    case 'modified': return '修改'
    default: return '无变更'
  }
}
</script>

<style scoped>
.query-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.change-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.field-comparison {
  margin-bottom: 20px;
}

.field-value {
  color: #303133;
  font-weight: 500;
}

.no-comparison-data {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.no-data {
  color: #909399;
  text-align: center;
  padding: 20px;
  font-style: italic;
}

.operation-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
