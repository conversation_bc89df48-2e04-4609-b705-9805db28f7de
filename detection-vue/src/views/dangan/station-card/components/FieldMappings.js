// 字段映射配置 - 将数据库字段名映射为中文显示名称

export const fieldMappings = {
  // 基本信息表 sc_service_base_info
  sc_service_base_info: {
    id: 'ID',
    facilityName: '设施名称',
    facilityType: '设施类型',
    branchCompany: '分公司',
    highwayName: '所在高速',
    constructionYear: '建成时间',
    hasInterchange: '互通情况',
    buildingComposition: '建筑物构成',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 用水情况表 sc_service_water_usage
  sc_service_water_usage: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    waterSource: '水源',
    dailyUsage: '日常用水量',
    peakUsage: '高峰期用水量',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 污水处理站表 sc_service_wastewater_station
  sc_service_wastewater_station: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    areaType: '区域类型',
    treatmentProcess: '处理工艺',
    designCapacity: '设计处理规模(t/d)',
    structureMaterial: '结构材质',
    constructionDate: '建成/改造时间',
    operationStatus: '运行状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 设备信息表 sc_service_equipment_info
  sc_service_equipment_info: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    equipmentCategory: '设备类别',
    equipmentType: '设备类型',
    location: '所处位置',
    quantity: '数量',
    manufacturerModel: '厂家型号',
    equipmentParameters: '设备参数',
    operationStatus: '运行状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 管网信息表 sc_service_pipeline_network
  sc_service_pipeline_network: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    areaType: '区域类型',
    rainSewageSeparation: '雨污分流情况',
    pipeDiameter: '管径(mm)',
    pipeMaterial: '管材材质',
    operationStatus: '运行情况',
    otherConditions: '其他情况',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 化粪池/隔油池表 sc_service_septic_tank
  sc_service_septic_tank: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    tankType: '池类型',
    areaType: '区域类型',
    sizeVolume: '尺寸容积',
    material: '材质',
    siltationStatus: '淤积情况',
    wastewaterAccess: '废水接入情况',
    otherConditions: '其他情况',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 生化系统表 sc_service_biochemical_system_info
  sc_service_biochemical_system_info: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    areaType: '区域类型',
    systemType: '系统类型',
    designCapacity: '设计处理能力',
    actualCapacity: '实际处理能力',
    operationStatus: '运行状态',
    nitrificationReflux: '硝化液回流运行状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 控制系统表 sc_service_control_system
  sc_service_control_system: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    controlType: '控制类型',
    automationLevel: '自动化程度',
    monitoringParameters: '监测参数',
    alarmFunction: '报警功能',
    operationStatus: '运行状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 二沉池表 sc_service_secondary_settling_tank_info
  sc_service_secondary_settling_tank_info: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    areaType: '区域类型',
    tankType: '池类型',
    sizeVolume: '尺寸容积',
    material: '材质',
    sludgeDisposal: '污泥处置方式',
    operationStatus: '运行状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 其他处理单元表 sc_service_other_treatment_units
  sc_service_other_treatment_units: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    unitType: '单元类型',
    unitName: '单元名称',
    treatmentFunction: '处理功能',
    designCapacity: '设计能力',
    operationStatus: '运行状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 排放信息表 sc_service_discharge_info
  sc_service_discharge_info: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    areaType: '区域类型',
    outletLocation: '排口位置',
    dischargeDestination: '排放去向',
    externalEnvironment: '外部环境',
    otherConditions: '其他情况',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  },

  // 系统评价表 sc_service_system_evaluation
  sc_service_system_evaluation: {
    id: 'ID',
    serviceAreaId: '服务区ID',
    evaluationContent: '评价内容',
    evaluator: '评价人',
    evaluationDate: '评价时间',
    complaintSituation: '投诉情况',
    complaintReason: '投诉原因',
    handlingSituation: '处理情况',
    remarks: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    modifier: '修改人'
  }
}

// 获取字段的中文名称
export function getFieldDisplayName(tableName, fieldName) {
  const tableMapping = fieldMappings[tableName]
  if (tableMapping && tableMapping[fieldName]) {
    return tableMapping[fieldName]
  }
  return fieldName // 如果没有映射，返回原字段名
}

// 格式化字段值显示
export function formatFieldValue(fieldName, value) {
  if (value === null || value === undefined) {
    return '-'
  }

  // 特殊字段格式化
  switch (fieldName) {
    case 'hasInterchange':
      return value ? '有' : '无'
    case 'createTime':
    case 'updateTime':
    case 'evaluationDate':
      if (typeof value === 'string' && value.includes('T')) {
        return value.replace('T', ' ').substring(0, 19)
      }
      return value
    case 'dailyUsage':
    case 'peakUsage':
    case 'designCapacity':
    case 'actualCapacity':
      return value + (typeof value === 'number' ? ' t/d' : '')
    case 'pipeDiameter':
      return value + (typeof value === 'number' ? ' mm' : '')
    default:
      return value
  }
}
