<template>
  <div class="app-container" v-loading="loading">
    <el-form
      :inline="true"
      ref="ruleFormRef"
      :model="formInline"
      class="demo-form-inline"
    >
      <el-form-item label="站点名称" prop="name">
        <el-input v-model="formInline.name" placeholder="站点名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">查 询</el-button>
        <el-button @click="resetForm(ruleFormRef)">重 置</el-button>
      </el-form-item>
    </el-form>
    <br />
    <div class="table-box">
      <!-- <el-card  shadow="hover"> -->
      <div class="demo-progress" v-for="(item, index) in dataArr" :key="index">
        <div class="demo-progress-view">
          <p class="demo-progress-text">站点名称:</p>
          <p class="demo-progress-projectName">{{ item.name }}</p>
        </div>
        <div class="demo-progress-view">
          <span class="demo-progress-text">设备总数:</span>
          <span>{{ item?.onlineDeviceCount + item?.offlineDeviceCount }}</span>
        </div>
        <div class="demo-progress-view">
          <span class="demo-progress-text">更新时间:</span>
          <span>{{ item.updateTime || item?.createTime }}</span>
        </div>
        <div class="demo-progress-progress">
          <el-button link size="large" type="primary">{{
            item.reportStatusName
          }}</el-button>
          <br />
          <el-icon size="20" color="#E6A23C"><Setting /></el-icon>
        </div>
        <div class="demo-progress-buts">
          <el-button type="primary" @click="handleStart(item)"
            >站点详情</el-button
          >
          <el-button type="success" @click="handleImport(item)"
            >导入数据</el-button
          >
          <el-button type="info" @click="handleViewData(item)"
            >查看档案</el-button
          >
        </div>
      </div>
      <!-- </el-card> -->
    </div>
    <br />
    <Pagination
      v-show="total > 0"
      :total="total"
      v-model:page="pageNum"
      v-model:limit="pageSize"
      @pagination="getList()"
    />
    
    <!-- 导入数据对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入站点档案数据"
      width="500px"
      :before-close="cancelImport"
    >
      <div class="import-content">
        <p class="station-info">站点名称：{{ currentStation?.name }}</p>
        <el-upload
          class="upload-demo"
          drag
          :before-upload="beforeUpload"
          :show-file-list="false"
          accept=".xlsx,.xls"
        >
          <div class="upload-area">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将Excel文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip">
              支持 .xlsx/.xls 格式文件，文件大小不超过10MB
            </div>
          </div>
        </el-upload>
        <div v-if="uploadFile" class="file-info">
          <p>已选择文件：{{ uploadFile.name }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelImport">取 消</el-button>
          <el-button type="primary" @click="executeImport" :disabled="!uploadFile">
            导 入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看档案数据对话框 -->
    <el-dialog
      v-model="dataViewDialogVisible"
      :title="`${currentStation?.name} - 站点档案数据`"
      width="95%"
      top="5vh"
    >
      <!-- 显示模式切换按钮 -->
      <div class="view-mode-switch" style="margin-bottom: 20px;margin-top:-40px">
        <el-radio-group v-model="viewMode" >
          <el-radio-button label="tab">标签页模式</el-radio-button>
          <el-radio-button label="full">同屏显示模式</el-radio-button>
        </el-radio-group>
      </div>

      <!-- Tab模式 -->
      <div v-if="viewMode === 'tab'">
        <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="基本信息" name="baseInfo">
          <div class="data-section">
            <div class="section-header">
              <h3>服务区基本情况</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('基本信息', 'sc_service_base_info', stationArchiveData.baseInfo?.id)"
              >
                查看日志记录
              </el-button>
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="服务区名称">{{ stationArchiveData.baseInfo?.facilityName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="分公司">{{ stationArchiveData.baseInfo?.branchCompany || '-' }}</el-descriptions-item>
              <el-descriptions-item label="所在高速">{{ stationArchiveData.baseInfo?.highwayName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="建成时间">{{ stationArchiveData.baseInfo?.constructionYear || '-' }}</el-descriptions-item>
              <el-descriptions-item label="互通情况">{{ stationArchiveData.baseInfo?.hasInterchange ? '有' : '无' }}</el-descriptions-item>
              <el-descriptions-item label="建筑物构成">{{ stationArchiveData.baseInfo?.buildingComposition || '-' }}</el-descriptions-item>
            </el-descriptions>

            <div class="section-header" style="margin-top: 20px;">
              <h3>用水情况</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('用水情况', 'sc_service_water_usage')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="水源">{{ stationArchiveData.waterUsage?.waterSource || '-' }}</el-descriptions-item>
              <el-descriptions-item label="日常用水量">{{ stationArchiveData.waterUsage?.dailyUsage || '-' }}</el-descriptions-item>
              <el-descriptions-item label="高峰期用水量">{{ stationArchiveData.waterUsage?.peakUsage || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="管网系统" name="pipelineNetwork">
          <div class="data-section">
            <div class="section-header">
              <h3>管网信息</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('管网系统', 'sc_service_pipeline_network')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-table :data="stationArchiveData.pipelineNetwork || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="rainSewageSeparation" label="雨污分流情况" width="150" />
              <el-table-column prop="pipeDiameter" label="管径(mm)" width="120" />
              <el-table-column prop="pipeMaterial" label="材质" width="200" />
              <el-table-column prop="operationStatus" label="运行情况" width="120" />
              <el-table-column prop="remark" label="其他情况" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="污水处理设施" name="wastewaterFacility">
          <div class="data-section">
            <div class="section-header">
              <h3>污水站基本情况</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('污水处理设施', 'sc_service_wastewater_station')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-table :data="stationArchiveData.wastewaterFacility || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="treatmentProcess" label="处理工艺" width="120" />
              <el-table-column prop="designCapacity" label="设计处理规模(t/d)" width="150" />
              <el-table-column prop="structureMaterial" label="结构材质" width="120" />
              <el-table-column prop="constructionDate" label="建成/改造时间" width="150" />
              <el-table-column prop="operationStatus" label="运行状态" width="120" />
              <el-table-column prop="remark" label="备注" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="主要设备" name="equipment">
          <div class="data-section">
            <div class="section-header">
              <h3>格栅设备</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('主要设备', 'sc_service_equipment_info')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-table :data="stationArchiveData.gratingEquipment || []" border style="margin-bottom: 20px;">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="equipmentType" label="具体类型" width="150" />
              <el-table-column prop="location" label="所处位置" width="120" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
              <el-table-column prop="equipmentParameters" label="设备参数" />
            </el-table>
            
            <h3>风机设备</h3>
            <el-table :data="stationArchiveData.fanEquipment || []" border style="margin-bottom: 20px;">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="equipmentType" label="具体类型" width="120" />
              <el-table-column prop="location" label="所处位置" width="120" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
              <el-table-column prop="equipmentParameters" label="设备参数" />
            </el-table>
            
            <h3>提升泵设备</h3>
            <el-table :data="stationArchiveData.pumpEquipment || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="equipmentType" label="具体类型" width="120" />
              <el-table-column prop="location" label="所处位置" width="120" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
              <el-table-column prop="equipmentParameters" label="设备参数" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="控制系统" name="controlSystem">
          <div class="data-section">
            <h3>控制系统信息</h3>
            <el-table :data="stationArchiveData.controlSystem || []" border>
              <el-table-column prop="area" label="区域" width="100" />
              <el-table-column prop="hasPlc" label="有无PLC" width="120" />
              <el-table-column prop="plcStatus" label="PLC运行状态" width="150" />
              <el-table-column prop="controlMethod" label="主要设备控制方式" width="180" />
              <el-table-column prop="electricalComponents" label="主要电气元件" width="200" />
              <el-table-column prop="otherSituation" label="其他情况" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="生化系统" name="biochemicalSystem">
          <div class="data-section">
            <h3>生化系统信息</h3>
            <el-table :data="stationArchiveData.biochemicalSystem || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="fillerType" label="填料形式" width="150" />
              <el-table-column prop="fillerStatus" label="填料状态" width="150" />
              <el-table-column prop="processParameters" label="工艺参数" width="200" />
              <el-table-column prop="sludgeConcentration" label="活性污泥浓度/状态" width="180" />
              <el-table-column prop="nitrificationReflux" label="硝化液回流运行状态" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="化粪池" name="septicTank">
          <div class="data-section">
            <div class="section-header">
              <h3>化粪池信息</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('化粪池', 'sc_service_septic_tank')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-table :data="stationArchiveData.septicTank || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="sizeVolume" label="尺寸容积" width="150" />
              <el-table-column prop="material" label="材质" width="120" />
              <el-table-column prop="siltationStatus" label="淤积情况" width="120" />
              <el-table-column prop="wastewaterAccess" label="废水接入情况" width="150" />
              <el-table-column prop="otherConditions" label="其他情况" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="隔油池" name="greaseTrap">
          <div class="data-section">
            <div class="section-header">
              <h3>隔油池信息</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('隔油池', 'sc_service_septic_tank')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-table :data="stationArchiveData.greaseTrap || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="sizeVolume" label="尺寸容积" width="150" />
              <el-table-column prop="material" label="材质" width="120" />
              <el-table-column prop="siltationStatus" label="淤积情况" width="120" />
              <el-table-column prop="wastewaterAccess" label="废水接入情况" width="150" />
              <el-table-column prop="otherConditions" label="其他情况" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="二沉池" name="secondarySettlingTank">
          <div class="data-section">
            <h3>二沉池信息</h3>
            <el-table :data="stationArchiveData.secondarySettlingTank || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="fillerType" label="填料形式" width="120" />
              <el-table-column prop="fillerStatus" label="填料状态" width="120" />
              <el-table-column prop="processParameters" label="工艺参数" width="200" />
              <el-table-column prop="sludgeRefluxStatus" label="污泥回流状态" width="150" />
              <el-table-column prop="floatingSludgeStatus" label="浮泥状态" width="250" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="其他处理单元" name="otherTreatmentUnits">
          <div class="data-section">
            <h3>其他处理单元信息</h3>
            <el-table :data="stationArchiveData.otherTreatmentUnits || []" border>
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="unitName" label="处理单元名称" width="150" />
              <el-table-column prop="operationStatus" label="运行状态" width="120" />
              <el-table-column prop="equipmentName" label="对应设备名称" width="150" />
              <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
              <el-table-column prop="equipmentParameters" label="设备参数" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="排放情况" name="dischargeInfo">
          <div class="data-section">
            <div class="section-header">
              <h3>排放信息</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('排放信息', 'sc_service_discharge_info')"
              >
                查看日志记录
              </el-button>
            </div>
            <el-table :data="stationArchiveData.dischargeInfo || []" border style="margin-bottom: 20px;">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="outletLocation" label="排口位置" width="150" />
              <el-table-column prop="dischargeDestination" label="排放去向" width="150" />
              <el-table-column prop="externalEnvironment" label="外部环境" width="150" />
              <el-table-column prop="otherConditions" label="其他情况" />
            </el-table>

            <div class="section-header" style="margin-top: 20px;">
              <h3>系统评价</h3>
              <el-button
                type="primary"
                size="small"
                @click="viewModuleChangeLog('系统评价', 'sc_service_system_evaluation', stationArchiveData.systemEvaluation?.id)"
              >
                查看日志记录
              </el-button>
            </div>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="评价内容">{{ stationArchiveData.systemEvaluation?.evaluationContent || '-' }}</el-descriptions-item>
              <el-descriptions-item label="评价人">{{ stationArchiveData.systemEvaluation?.evaluator || '-' }}</el-descriptions-item>
              <el-descriptions-item label="评价时间">{{ stationArchiveData.systemEvaluation?.evaluationDate || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 同屏显示模式 -->
      <div v-if="viewMode === 'full'" class="full-screen-view">
          <!-- 1. 基本情况 -->
          <div class="module-section">
            <h2 class="module-title">基本情况</h2>
            <h3 class="section-subtitle">服务区基本情况</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="服务区名称">{{ stationArchiveData.baseInfo?.facilityName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="分公司">{{ stationArchiveData.baseInfo?.branchCompany || '-' }}</el-descriptions-item>
              <el-descriptions-item label="所在高速">{{ stationArchiveData.baseInfo?.highwayName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="建成时间">{{ stationArchiveData.baseInfo?.constructionYear || '-' }}</el-descriptions-item>
              <el-descriptions-item label="互通情况">{{ stationArchiveData.baseInfo?.hasInterchange ? '有' : '无' }}</el-descriptions-item>
              <el-descriptions-item label="建筑物构成">{{ stationArchiveData.baseInfo?.buildingComposition || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 2. 用水情况 -->
          <div class="module-section">
            <h2 class="module-title">用水情况</h2>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="水源">{{ stationArchiveData.waterUsage?.waterSource || '-' }}</el-descriptions-item>
              <el-descriptions-item label="日常用水量">{{ stationArchiveData.waterUsage?.dailyUsage || '-' }}</el-descriptions-item>
              <el-descriptions-item label="高峰期用水量">{{ stationArchiveData.waterUsage?.peakUsage || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 3. 管网 -->
          <div class="module-section">
            <h2 class="module-title">管网</h2>
            <el-table :data="stationArchiveData.pipelineNetwork || []" border v-if="stationArchiveData.pipelineNetwork && stationArchiveData.pipelineNetwork.length > 0">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="rainSewageSeparation" label="雨污分流" width="120" />
              <el-table-column prop="pipelineLayout" label="管网布置" width="150" />
              <el-table-column prop="pipeDiameter" label="管径" width="100" />
              <el-table-column prop="material" label="材质" width="120" />
              <el-table-column prop="pipelineCondition" label="管网状况" />
            </el-table>
            <div v-else class="empty-state">
              <div class="empty-icon">📊</div>
              <div class="empty-text">暂无管网数据</div>
              <div class="empty-description">请先导入相关数据</div>
            </div>
          </div>

          <!-- 4. 化粪池 -->
          <div class="module-section">
            <h2 class="module-title">化粪池</h2>
            <el-table :data="stationArchiveData.septicTank || []" border v-if="stationArchiveData.septicTank && stationArchiveData.septicTank.length > 0">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="sizeVolume" label="尺寸容积" width="120" />
              <el-table-column prop="material" label="材质" width="120" />
              <el-table-column prop="siltationStatus" label="淤积情况" width="120" />
              <el-table-column prop="wastewaterAccess" label="废水接入情况" width="150" />
              <el-table-column prop="otherConditions" label="其他情况" />
            </el-table>
            <div v-else class="empty-state">
              <div class="empty-icon">🏗️</div>
              <div class="empty-text">暂无化粪池数据</div>
              <div class="empty-description">请先导入相关数据</div>
            </div>
          </div>

          <!-- 5. 隔油池 -->
          <div class="module-section">
            <h2 class="module-title">隔油池</h2>
            <el-table :data="stationArchiveData.greaseTrap || []" border v-if="stationArchiveData.greaseTrap && stationArchiveData.greaseTrap.length > 0">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="sizeVolume" label="尺寸容积" width="120" />
              <el-table-column prop="material" label="材质" width="120" />
              <el-table-column prop="siltationStatus" label="淤积情况" width="120" />
              <el-table-column prop="wastewaterAccess" label="废水接入情况" width="150" />
              <el-table-column prop="otherConditions" label="其他情况" />
            </el-table>
            <div v-else class="empty-state">
              <div class="empty-icon">🛢️</div>
              <div class="empty-text">暂无隔油池数据</div>
              <div class="empty-description">请先导入相关数据</div>
            </div>
          </div>

          <!-- 6. 污水站基本情况 -->
          <div class="module-section">
            <h2 class="module-title">污水站基本情况</h2>
            <el-table :data="stationArchiveData.wastewaterFacility || []" border v-if="stationArchiveData.wastewaterFacility && stationArchiveData.wastewaterFacility.length > 0">
              <el-table-column prop="areaType" label="区域" width="100" />
              <el-table-column prop="treatmentProcess" label="处理工艺" width="120" />
              <el-table-column prop="designCapacity" label="设计处理规模(t/d)" width="150" />
              <el-table-column prop="structureMaterial" label="结构材质" width="120" />
              <el-table-column prop="constructionDate" label="建成/改造时间" width="150" />
              <el-table-column prop="operationStatus" label="运行状态" width="120" />
              <el-table-column prop="remark" label="备注" />
            </el-table>
            <div v-else class="empty-state">
              <div class="empty-icon">🏭</div>
              <div class="empty-text">暂无污水站数据</div>
              <div class="empty-description">请先导入相关数据</div>
            </div>
          </div>

          <!-- 7. 主要设备（格栅） -->
          <div class="module-section">
            <h2 class="module-title">主要设备（格栅）</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.gratingEquipment || []" border>
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="equipmentSubtype" label="设备子类型" width="120" />
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="operationStatus" label="运行状态" width="120" />
                <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
                <el-table-column prop="equipmentParameters" label="设备参数" />
              </el-table>
            </div>
          </div>

          <!-- 8. 主要设备（风机） -->
          <div class="module-section">
            <h2 class="module-title">主要设备（风机）</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.fanEquipment || []" border>
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="equipmentSubtype" label="设备子类型" width="120" />
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="operationStatus" label="运行状态" width="120" />
                <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
                <el-table-column prop="equipmentParameters" label="设备参数" />
              </el-table>
            </div>
          </div>

          <!-- 9. 主要设备（提升泵） -->
          <div class="module-section">
            <h2 class="module-title">主要设备（提升泵）</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.pumpEquipment || []" border>
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="equipmentSubtype" label="设备子类型" width="120" />
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="operationStatus" label="运行状态" width="120" />
                <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
                <el-table-column prop="equipmentParameters" label="设备参数" />
              </el-table>
            </div>
          </div>

          <!-- 10. 主要设备（控制系统） -->
          <div class="module-section">
            <h2 class="module-title">主要设备（控制系统）</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.controlSystem || []" border>
                <el-table-column prop="area" label="区域" width="100" />
                <el-table-column prop="hasPlc" label="有无PLC" width="120" />
                <el-table-column prop="plcStatus" label="PLC运行状态" width="150" />
                <el-table-column prop="controlMethod" label="主要设备控制方式" width="180" />
                <el-table-column prop="electricalComponents" label="主要电气元件" width="200" />
                <el-table-column prop="otherSituation" label="其他情况" />
              </el-table>
            </div>
          </div>

          <!-- 11. 生化系统 -->
          <div class="module-section">
            <h2 class="module-title">生化系统</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.biochemicalSystem || []" border>
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="fillerType" label="填料形式" width="150" />
                <el-table-column prop="fillerVolume" label="填料体积" width="120" />
                <el-table-column prop="aerationMethod" label="曝气方式" width="150" />
                <el-table-column prop="operationParameters" label="运行参数" />
              </el-table>
            </div>
          </div>

          <!-- 12. 二沉池 -->
          <div class="module-section">
            <h2 class="module-title">二沉池</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.secondarySettlingTank || []" border>
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="fillerType" label="填料形式" width="120" />
                <el-table-column prop="fillerStatus" label="填料状态" width="120" />
                <el-table-column prop="processParameters" label="工艺参数" width="200" />
                <el-table-column prop="sludgeRefluxStatus" label="污泥回流状态" width="150" />
                <el-table-column prop="floatingSludgeStatus" label="浮泥状态" width="250" />
              </el-table>
            </div>
          </div>

          <!-- 13. 其他处理单元/设备 -->
          <div class="module-section">
            <h2 class="module-title">其他处理单元/设备</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.otherTreatmentUnits || []" border>
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="unitName" label="处理单元名称" width="150" />
                <el-table-column prop="operationStatus" label="运行状态" width="120" />
                <el-table-column prop="equipmentName" label="对应设备名称" width="150" />
                <el-table-column prop="manufacturerModel" label="厂家型号" width="150" />
                <el-table-column prop="equipmentParameters" label="设备参数" />
              </el-table>
            </div>
          </div>

          <!-- 14. 排放情况 -->
          <div class="module-section">
            <h2 class="module-title">排放情况</h2>
            <div class="data-section">
              <el-table :data="stationArchiveData.dischargeInfo || []" border style="margin-bottom: 20px;">
                <el-table-column prop="areaType" label="区域" width="100" />
                <el-table-column prop="outletLocation" label="排口位置" width="150" />
                <el-table-column prop="dischargeMethod" label="排放方式" width="120" />
                <el-table-column prop="treatmentStandard" label="处理标准" width="150" />
                <el-table-column prop="monitoringFrequency" label="监测频次" width="120" />
                <el-table-column prop="dischargeVolume" label="排放量" />
              </el-table>
            </div>
          </div>

          <!-- 15. 系统整体评价 -->
          <div class="module-section">
            <h2 class="module-title">系统整体评价</h2>
            <div class="data-section">
              <el-descriptions :column="1" border v-if="stationArchiveData.systemEvaluation">
                <el-descriptions-item label="评价内容">{{ stationArchiveData.systemEvaluation.evaluationContent || '-' }}</el-descriptions-item>
                <el-descriptions-item label="评价人">{{ stationArchiveData.systemEvaluation.evaluator || '-' }}</el-descriptions-item>
                <el-descriptions-item label="评价日期">{{ stationArchiveData.systemEvaluation.evaluationDate || '-' }}</el-descriptions-item>
              </el-descriptions>
              <div v-else class="no-data">暂无系统评价数据</div>
            </div>
          </div>

          <!-- 16. 环保投诉情况及原因 -->
          <div class="module-section">
            <h2 class="module-title">环保投诉情况及原因</h2>
            <div class="data-section">
              <el-descriptions :column="1" border v-if="stationArchiveData.systemEvaluation">
                <el-descriptions-item label="投诉情况">{{ stationArchiveData.systemEvaluation.complaintSituation || '-' }}</el-descriptions-item>
                <el-descriptions-item label="投诉原因">{{ stationArchiveData.systemEvaluation.complaintReason || '-' }}</el-descriptions-item>
                <el-descriptions-item label="处理情况">{{ stationArchiveData.systemEvaluation.handlingSituation || '-' }}</el-descriptions-item>
              </el-descriptions>
              <div v-else class="no-data">暂无投诉记录</div>
            </div>
          </div>

          <!-- 17. 其他问题 -->
          <div class="module-section">
            <h2 class="module-title">其他问题</h2>
            <el-descriptions :column="1" border v-if="stationArchiveData.systemEvaluation">
              <el-descriptions-item label="其他问题">{{ stationArchiveData.systemEvaluation.otherIssues || '-' }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ stationArchiveData.systemEvaluation.remarks || '-' }}</el-descriptions-item>
            </el-descriptions>
            <div v-else class="empty-state">
              <div class="empty-icon">📝</div>
              <div class="empty-text">暂无其他问题记录</div>
              <div class="empty-description">如有其他问题，请在系统评价中添加</div>
            </div>
          </div>
      </div>
    </el-dialog>

    <!-- 变更日志对话框 -->
    <ChangeLogDialog
      v-model="changeLogDialogVisible"
      :facility-name="changeLogParams.facilityName"
      :table-name="changeLogParams.tableName"
      :record-id="changeLogParams.recordId"
      :module-name="changeLogParams.moduleName"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { listAll } from "@/api/dangan/index";
import { ElMessage, ElMessageBox, ElUpload } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { importWastewaterData, getWastewaterDataByName } from "@/api/wastewater/facility";
import ChangeLogDialog from "./components/ChangeLogDialog.vue";
const router = useRouter();
const total = ref<number>(0);
const pageNum = ref<number>(1);
const pageSize = ref<number>(10);
const formInline = ref<any>({
  name: "",
});
const form = ref<any>({
  projectCode: "",
});
let dataArr = ref<any[]>([{}]);
const loading = ref(false);
const getList = async () => {
  try {
    loading.value = true;
    const res = await listAll({ name: formInline.value.name });
    dataArr.value = res.data;
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};

const onSubmit = () => {
  getList();
};
const ruleFormRef = ref<any>(null);
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  getList();
};
const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`);
};
const handleStart = (item: any) => {
  router.push({ path: "/dangan/station-card/details", query: { stationId: item.id } });
};
const dialogVisible = ref<boolean>(false);
const importDialogVisible = ref<boolean>(false);
const dataViewDialogVisible = ref<boolean>(false);
const currentStation = ref<any>(null);
const uploadFile = ref<any>(null);
const activeTab = ref<string>('baseInfo');
const viewMode = ref<string>('tab'); // 显示模式：tab(标签页模式) 或 full(同屏模式)
const stationArchiveData = ref<any>({});

// 变更日志相关
const changeLogDialogVisible = ref(false);
const changeLogParams = ref({
  facilityName: '',
  tableName: '',
  recordId: '',
  moduleName: ''
});

const addProject = () => {
  dialogVisible.value = true;
};

// 处理导入数据
const handleImport = (item: any) => {
  currentStation.value = item;
  importDialogVisible.value = true;
};

// 处理查看档案数据
const handleViewData = async (item: any) => {
  currentStation.value = item;
  try {
    loading.value = true;
    const res = await getWastewaterDataByName(item.name);
    console.log('获取到的档案数据:', res.data); // 调试日志
    
    // 处理后端返回的数据结构，将数组转换为前端需要的格式
    const rawData = res.data || {};
    const processedData = {
      // 基本信息 - 取数组第一个元素
      baseInfo: rawData.baseInfo && rawData.baseInfo.length > 0 ? rawData.baseInfo[0] : null,
      // 用水情况 - 取数组第一个元素
      waterUsage: rawData.waterUsage && rawData.waterUsage.length > 0 ? rawData.waterUsage[0] : null,
      // 管网信息 - 保持数组格式
      pipelineNetwork: rawData.pipelineNetwork || [],
      // 污水站信息 - 保持数组格式
      wastewaterFacility: rawData.wastewaterStation || [],
      // 设备信息 - 按类型分组
      gratingEquipment: (rawData.equipment || []).filter(item => item.equipmentCategory === '格栅'),
      fanEquipment: (rawData.equipment || []).filter(item => item.equipmentCategory === '风机'),
      pumpEquipment: (rawData.equipment || []).filter(item => item.equipmentCategory === '提升泵'),
      // 控制系统 - 使用独立的控制系统表数据
      controlSystem: rawData.controlSystem || [],
      // 生化系统
      biochemicalSystem: rawData.biochemicalSystem || [],
      // 化粪池和隔油池
      septicTank: rawData.septicTank || [],
      greaseTrap: rawData.greaseTrap || [],
      // 二沉池
      secondarySettlingTank: rawData.secondarySettlingTank || [],
      // 其他处理单元
      otherTreatmentUnits: rawData.otherTreatmentUnits || [],
      // 排放信息
      dischargeInfo: rawData.dischargeInfo || [],
      // 系统评价 - 取数组第一个元素
      systemEvaluation: rawData.systemEvaluation && rawData.systemEvaluation.length > 0 ? rawData.systemEvaluation[0] : null
    };
    
    stationArchiveData.value = processedData;
    console.log('处理后的档案数据:', processedData); // 调试日志
    dataViewDialogVisible.value = true;
    loading.value = false;
  } catch (error) {
    console.error('获取档案数据失败:', error);
    loading.value = false;
    ElMessage.error('获取档案数据失败');
  }
};

// 文件上传前的校验
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!');
    return false;
  }
  uploadFile.value = file;
  return false; // 阻止自动上传
};

// 执行导入
const executeImport = async () => {
  if (!uploadFile.value) {
    ElMessage.error('请选择要上传的文件');
    return;
  }
  if (!currentStation.value) {
    ElMessage.error('请选择站点');
    return;
  }
  
  try {
    loading.value = true;
    const formData = new FormData();
    formData.append('file', uploadFile.value);
    formData.append('facilityName', currentStation.value.name);
    
    const res = await importWastewaterData(formData);
    if (res.code === 200) {
      ElMessage.success(res.msg || '导入成功');
      importDialogVisible.value = false;
      uploadFile.value = null;
    } else {
      ElMessage.error(res.msg || '导入失败');
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    ElMessage.error('导入失败');
  }
};

// 取消导入
const cancelImport = () => {
  importDialogVisible.value = false;
  uploadFile.value = null;
};

// 查看模块变更日志
const viewModuleChangeLog = (moduleName: string, tableName: string, recordId?: string) => {
  changeLogParams.value = {
    facilityName: currentStation.value?.name || '',
    tableName: tableName,
    recordId: recordId || '',
    moduleName: moduleName
  };
  changeLogDialogVisible.value = true;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.card-view {
  min-height: calc(100vh - 260px);
}
.table-box {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(300px, 1fr)
  ); /* 自动填充，最小200px */
  gap: 20px; /* 项目间距 */
  justify-content: space-between;
}
.demo-progress {
  width: 100%;
  height: 180px;
  box-sizing: border-box;
  padding: 16px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  border-radius: 4px;
  .demo-progress-view {
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    display: flex;
    align-items: center;
    .demo-progress-text {
      text-align: justify;
      text-justify: inter-word; /* 单词间调整间距 */
      width: 80px;
      color: #2c2a2a;
    }
    .demo-progress-projectName {
      margin: 0;
      padding: 0;
      width: 150px;
      line-height: 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .demo-progress-progress {
    position: absolute;
    right: 10px;
    top: 16px;
    width: 40px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .demo-progress-buts {
    height: 60px;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0px;
    padding-bottom: 16px;
    // background: rgba($color: #000000, $alpha: 0.1);
    display: flex;
    justify-content: space-around;
    align-items: end;
    gap: 8px;
    padding: 0 16px 16px;
    
    .el-button {
      flex: 1;
      font-size: 12px;
      padding: 8px 12px;
    }
  }
}
.demo-progress:hover {
  background: rgba($color: #000000, $alpha: 0.1);
  .demo-progress-buts {
    bottom: 0;
  }
}

// 导入对话框样式
.import-content {
  .station-info {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #409eff;
  }
  
  .upload-area {
    text-align: center;
    padding: 40px 0;
    
    .el-icon--upload {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }
    
    .el-upload__text {
      color: #606266;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .el-upload__tip {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .file-info {
    margin-top: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px;
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

// 数据展示对话框样式
.data-section {
  h3 {
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    color: #303133;
    font-size: 16px;
    font-weight: bold;
  }
  
  .el-descriptions {
    margin-bottom: 24px;
  }
  
  .el-table {
    margin-bottom: 20px;
    
    .el-table__header th {
      background: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }
    
    .el-table__row:hover {
      background: #f5f7fa;
    }
  }
}

// 标签页样式优化
:deep(.el-tabs--card) {
  .el-tabs__header {
    margin-bottom: 20px;
  }
  
  .el-tabs__item {
    font-weight: bold;
    
    &.is-active {
      color: #409eff;
    }
  }
  
  .el-tabs__content {
    padding: 0 16px;
  }
}

// 显示模式切换按钮样式
.view-mode-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  
  .el-radio-group {
    .el-radio-button__inner {
      padding: 8px 20px;
      font-weight: 500;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: #409eff;
      border-color: #409eff;
      color: #fff;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    }
  }
}

// 同屏模式样式
.full-screen-view {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  margin-top: -20px;
  
  .module-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .module-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 3px solid #409eff;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #409eff, #67c23a);
        border-radius: 2px;
      }
    }
    
    .el-descriptions {
      margin-bottom: 20px;
      
      :deep(.el-descriptions__header) {
        margin-bottom: 16px;
      }
      
      :deep(.el-descriptions__body) {
        .el-descriptions__table {
          border-radius: 6px;
          overflow: hidden;
        }
        
        .el-descriptions__label {
          background: #f8f9fa;
          font-weight: 600;
          color: #606266;
        }
        
        .el-descriptions__content {
          background: #fff;
          color: #303133;
        }
      }
    }
    
    .el-table {
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      :deep(.el-table__header) {
        th {
          background: linear-gradient(135deg, #f5f7fa, #e8ecf0);
          color: #606266;
          font-weight: bold;
          border-bottom: 1px solid #ebeef5;
        }
      }
      
      :deep(.el-table__body) {
        tr:nth-child(even) {
          background: #fafbfc;
        }
        
        tr:hover {
          background: #ecf5ff !important;
        }
        
        td {
          border-bottom: 1px solid #ebeef5;
        }
      }
    }
    
    .section-subtitle {
      font-size: 16px;
      font-weight: 600;
      color: #409eff;
      margin: 24px 0 16px 0;
      padding-left: 12px;
      border-left: 4px solid #409eff;
      background: linear-gradient(90deg, rgba(64, 158, 255, 0.1), transparent);
      padding: 8px 0 8px 12px;
      border-radius: 4px;
    }
  }
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// 模块标题和按钮样式
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }
}

// 空数据状态样式
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    opacity: 0.8;
  }
}
</style>