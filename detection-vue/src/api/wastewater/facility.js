import request from '@/utils/request'

// 查询服务区/收费站基本信息列表
export function listServiceBaseInfo(query) {
  return request({
    url: '/wastewater/facility/list',
    method: 'get',
    params: query
  })
}

// 查询服务区/收费站基本信息详细
export function getServiceBaseInfo(id) {
  return request({
    url: '/wastewater/facility/' + id,
    method: 'get'
  })
}

// 根据设施名称获取完整的污水处理设施数据
export function getWastewaterDataByFacility(facilityName) {
  return request({
    url: '/wastewater/facility/getByFacilityName/' + facilityName,
    method: 'get'
  })
}

// 新增服务区/收费站基本信息
export function addServiceBaseInfo(data) {
  return request({
    url: '/wastewater/facility',
    method: 'post',
    data: data
  })
}

// 修改服务区/收费站基本信息
export function updateServiceBaseInfo(data) {
  return request({
    url: '/wastewater/facility',
    method: 'put',
    data: data
  })
}

// 删除服务区/收费站基本信息
export function delServiceBaseInfo(ids) {
  return request({
    url: '/wastewater/facility/' + ids,
    method: 'delete'
  })
}

// 导出服务区/收费站基本信息
export function exportServiceBaseInfo(query) {
  return request({
    url: '/wastewater/facility/export',
    method: 'post',
    data: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/wastewater/facility/importTemplate',
    method: 'post'
  })
}

// 导入污水处理设施数据
export function importWastewaterData(data) {
  return request({
    url: '/wastewater/facility/importData',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取服务区/收费站基本信息记录数
export function countServiceBaseInfo(query) {
  return request({
    url: '/wastewater/facility/count',
    method: 'get',
    params: query
  })
}

// 根据设施名称查询档案变更日志
export function getChangeLogByFacility(facilityName, query) {
  return request({
    url: '/wastewater/facility/changeLog/' + facilityName,
    method: 'get',
    params: query
  })
}

// 根据表名和记录ID查询档案变更日志
export function getChangeLogByRecord(tableName, recordId, query) {
  return request({
    url: '/wastewater/facility/changeLog/' + tableName + '/' + recordId,
    method: 'get',
    params: query
  })
}

// 根据设施名称获取完整的污水处理设施数据（别名函数）
export function getWastewaterDataByName(facilityName) {
  return request({
    url: '/wastewater/facility/getByFacilityName/' + facilityName,
    method: 'get'
  })
}
