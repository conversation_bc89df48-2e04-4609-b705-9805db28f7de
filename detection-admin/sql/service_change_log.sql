-- 服务区/收费站档案变更日志表
CREATE TABLE `sc_service_change_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更的表名',
  `record_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更记录的主键ID',
  `operation_type` enum('INSERT','UPDATE','DELETE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型：新增/修改/删除',
  `old_values` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变更前的完整记录（JSON格式）',
  `new_values` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变更后的完整记录（JSON格式）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` bigint DEFAULT NULL COMMENT '创建人',
  `modifier` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_table_record` (`table_name`,`record_id`) USING BTREE,
  KEY `idx_table_operation` (`table_name`,`operation_type`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务区/收费站档案变更日志表';

-- 插入一些示例数据（可选）
-- INSERT INTO `sc_service_change_log` (`table_name`, `record_id`, `operation_type`, `old_values`, `new_values`, `remark`, `creator`) 
-- VALUES ('sc_service_base_info', '1', 'INSERT', NULL, '{"id":1,"facilityName":"测试服务区","facilityType":1}', '新增基本信息', 1);
