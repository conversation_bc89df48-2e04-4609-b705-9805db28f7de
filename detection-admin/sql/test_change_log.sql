-- 测试变更日志功能的SQL脚本

-- 1. 检查变更日志表是否存在
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'sc_service_change_log';

-- 2. 检查表结构
DESCRIBE sc_service_change_log;

-- 3. 插入测试数据
INSERT INTO `sc_service_change_log` (
    `table_name`, 
    `record_id`, 
    `operation_type`, 
    `old_values`, 
    `new_values`, 
    `remark`, 
    `creator`
) VALUES (
    'sc_service_base_info', 
    '1', 
    'UPDATE', 
    '{"id":1,"facilityName":"测试服务区","facilityType":1}', 
    '{"id":1,"facilityName":"测试服务区(修改后)","facilityType":1}', 
    '测试变更日志功能', 
    1
);

-- 4. 查询测试数据
SELECT * FROM sc_service_change_log WHERE table_name = 'sc_service_base_info' AND record_id = '1';

-- 5. 测试按表名和记录ID查询
SELECT scl.*, su.user_name as operator_name
FROM sc_service_change_log scl
LEFT JOIN sys_user su ON scl.creator = su.user_id
WHERE scl.table_name = 'sc_service_base_info' 
AND scl.record_id = '1'
ORDER BY scl.create_time DESC;

-- 6. 清理测试数据（可选）
-- DELETE FROM sc_service_change_log WHERE remark = '测试变更日志功能';
