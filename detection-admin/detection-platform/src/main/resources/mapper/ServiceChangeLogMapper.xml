<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceChangeLogMapper">
    
    <resultMap type="ServiceChangeLog" id="ServiceChangeLogResult">
        <result property="id"    column="id"    />
        <result property="tableName"    column="table_name"    />
        <result property="recordId"    column="record_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="oldValues"    column="old_values"    />
        <result property="newValues"    column="new_values"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <result property="facilityName"    column="facility_name"    />
        <result property="operatorName"    column="operator_name"    />
    </resultMap>

    <sql id="selectServiceChangeLogVo">
        select scl.id, scl.table_name, scl.record_id, scl.operation_type, scl.old_values, scl.new_values, 
               scl.remark, scl.create_time, scl.update_time, scl.creator, scl.modifier,
               sbi.facility_name, su.user_name as operator_name
        from sc_service_change_log scl
        left join sc_service_base_info sbi on scl.table_name = 'sc_service_base_info' and scl.record_id = sbi.id
        left join sys_user su on scl.creator = su.user_id
    </sql>

    <select id="selectServiceChangeLogList" parameterType="ServiceChangeLog" resultMap="ServiceChangeLogResult">
        <include refid="selectServiceChangeLogVo"/>
        <where>
            <if test="tableName != null  and tableName != ''"> and scl.table_name = #{tableName}</if>
            <if test="recordId != null  and recordId != ''"> and scl.record_id = #{recordId}</if>
            <if test="operationType != null  and operationType != ''"> and scl.operation_type = #{operationType}</if>
            <if test="facilityName != null  and facilityName != ''"> and sbi.facility_name like concat('%', #{facilityName}, '%')</if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        order by scl.create_time desc
    </select>
    
    <select id="selectServiceChangeLogById" parameterType="Long" resultMap="ServiceChangeLogResult">
        <include refid="selectServiceChangeLogVo"/>
        where scl.id = #{id}
    </select>

    <select id="selectServiceChangeLogByTableAndRecord" resultMap="ServiceChangeLogResult">
        <include refid="selectServiceChangeLogVo"/>
        where scl.table_name = #{tableName} and scl.record_id = #{recordId}
        order by scl.create_time desc
    </select>

    <select id="selectServiceChangeLogByFacilityName" parameterType="String" resultMap="ServiceChangeLogResult">
        <include refid="selectServiceChangeLogVo"/>
        where sbi.facility_name = #{facilityName}
        order by scl.create_time desc
    </select>
        
    <insert id="insertServiceChangeLog" parameterType="ServiceChangeLog" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_change_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">table_name,</if>
            <if test="recordId != null and recordId != ''">record_id,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="oldValues != null">old_values,</if>
            <if test="newValues != null">new_values,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">#{tableName},</if>
            <if test="recordId != null and recordId != ''">#{recordId},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="oldValues != null">#{oldValues},</if>
            <if test="newValues != null">#{newValues},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceChangeLog" parameterType="ServiceChangeLog">
        update sc_service_change_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="recordId != null and recordId != ''">record_id = #{recordId},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="oldValues != null">old_values = #{oldValues},</if>
            <if test="newValues != null">new_values = #{newValues},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceChangeLogById" parameterType="Long">
        delete from sc_service_change_log where id = #{id}
    </delete>

    <delete id="deleteServiceChangeLogByIds" parameterType="String">
        delete from sc_service_change_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceChangeLogByTableAndRecord">
        delete from sc_service_change_log where table_name = #{tableName} and record_id = #{recordId}
    </delete>
</mapper>
