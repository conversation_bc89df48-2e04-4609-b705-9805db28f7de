package com.tunnel.service.impl;

import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.*;
import com.tunnel.mapper.*;
import com.tunnel.service.WastewaterFacilityService;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.tunnel.common.utils.StringUtils.isNotEmpty;

/**
 * 污水处理设施Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Service
public class WastewaterFacilityServiceImpl implements WastewaterFacilityService {
    private static final Logger log = LoggerFactory.getLogger(WastewaterFacilityServiceImpl.class);

    @Autowired
    private ServiceBaseInfoMapper baseInfoMapper;

    @Autowired
    private ServiceWaterUsageMapper waterUsageMapper;

    @Autowired
    private ServiceWastewaterStationMapper wastewaterStationMapper;

    @Autowired
    private ServiceEquipmentInfoMapper equipmentInfoMapper;

    @Autowired
    private ServicePipelineNetworkMapper pipelineNetworkMapper;

    @Autowired
    private ServiceSepticTankMapper septicTankMapper;


    @Autowired
    private ServiceBiochemicalSystemInfoMapper biochemicalSystemInfoMapper;

    @Autowired
    private ServiceControlSystemMapper controlSystemMapper;

    @Autowired
    private ServiceSecondarySettlingTankInfoMapper secondarySettlingTankInfoMapper;

    @Autowired
    private ServiceOtherTreatmentUnitsMapper otherTreatmentUnitsMapper;

    @Autowired
    private ServiceDischargeInfoMapper dischargeInfoMapper;

    @Autowired
    private ServiceSystemEvaluationMapper systemEvaluationMapper;

    /**
     * 查询服务区/收费站基本信息
     *
     * @param id 服务区/收费站基本信息主键
     * @return 服务区/收费站基本信息
     */
    @Override
    public ServiceBaseInfo selectServiceBaseInfoById(Long id) {
        return baseInfoMapper.selectServiceBaseInfoById(id);
    }

    /**
     * 查询服务区/收费站基本信息列表
     *
     * @param baseInfo 服务区/收费站基本信息
     * @return 服务区/收费站基本信息
     */
    @Override
    public List<ServiceBaseInfo> selectServiceBaseInfoList(ServiceBaseInfo baseInfo) {
        return baseInfoMapper.selectServiceBaseInfoList(baseInfo);
    }

    /**
     * 新增服务区/收费站基本信息
     *
     * @param baseInfo 服务区/收费站基本信息
     * @return 结果
     */
    @Override
    public int insertServiceBaseInfo(ServiceBaseInfo baseInfo) {
        baseInfo.setCreateTime(new Date());
        return baseInfoMapper.insertServiceBaseInfo(baseInfo);
    }

    /**
     * 修改服务区/收费站基本信息
     *
     * @param baseInfo 服务区/收费站基本信息
     * @return 结果
     */
    @Override
    public int updateServiceBaseInfo(ServiceBaseInfo baseInfo) {
        baseInfo.setUpdateTime(new Date());
        return baseInfoMapper.updateServiceBaseInfo(baseInfo);
    }

    /**
     * 批量删除服务区/收费站基本信息
     *
     * @param ids 需要删除的服务区/收费站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteServiceBaseInfoByIds(Long[] ids) {
        return baseInfoMapper.deleteServiceBaseInfoByIds(ids);
    }

    /**
     * 根据设施名称获取所有相关数据
     *
     * @param facilityName 设施名称
     * @return 包含所有相关表数据的Map
     */
    @Override
    public Map<String, Object> selectAllDataByFacilityName(String facilityName) {
        Map<String, Object> result = new HashMap<>();
        
        // 查询基本信息
        ServiceBaseInfo baseInfo = baseInfoMapper.selectServiceBaseInfoByName(facilityName);
        if (baseInfo != null) {
            result.put("baseInfo", Arrays.asList(baseInfo));
            
            Long serviceAreaId = baseInfo.getId();
            
            // 查询用水情况
            List<ServiceWaterUsage> waterUsageList = waterUsageMapper.selectServiceWaterUsageByServiceAreaId(serviceAreaId);
            result.put("waterUsage", waterUsageList);
            
            // 查询管网信息
            List<ServicePipelineNetwork> pipelineNetworkList = pipelineNetworkMapper.selectServicePipelineNetworkByServiceAreaId(serviceAreaId);
            result.put("pipelineNetwork", pipelineNetworkList);
            
            // 查询化粪池信息
            List<ServiceSepticTank> septicTankList = septicTankMapper.selectServiceSepticTankByServiceAreaIdAndType(serviceAreaId, "化粪池");
            result.put("septicTank", septicTankList);
            
            // 查询隔油池信息（使用统一的septic tank表，按类型过滤）
            List<ServiceSepticTank> greaseTrapList = septicTankMapper.selectServiceSepticTankByServiceAreaIdAndType(serviceAreaId, "隔油池");
            result.put("greaseTrap", greaseTrapList);
            
            // 查询污水站情况
            List<ServiceWastewaterStation> wastewaterStationList = wastewaterStationMapper.selectServiceWastewaterStationByServiceAreaId(serviceAreaId);
            result.put("wastewaterStation", wastewaterStationList);
            
            // 查询设备信息
            List<ServiceEquipmentInfo> equipmentList = equipmentInfoMapper.selectServiceEquipmentInfoByServiceAreaId(serviceAreaId);
            result.put("equipment", equipmentList);
            
            // 查询生化系统信息
            List<ServiceBiochemicalSystemInfo> biochemicalSystemList = biochemicalSystemInfoMapper.selectServiceBiochemicalSystemInfoByServiceAreaId(serviceAreaId);
            result.put("biochemicalSystem", biochemicalSystemList);
            
            // 查询二沉池信息
            List<ServiceSecondarySettlingTankInfo> secondarySettlingTankList = secondarySettlingTankInfoMapper.selectServiceSecondarySettlingTankInfoByServiceAreaId(serviceAreaId);
            result.put("secondarySettlingTank", secondarySettlingTankList);
            
            // 查询其他处理单元/设备
            List<ServiceOtherTreatmentUnits> otherTreatmentUnitsList = otherTreatmentUnitsMapper.selectServiceOtherTreatmentUnitsByServiceAreaId(serviceAreaId);
            result.put("otherTreatmentUnits", otherTreatmentUnitsList);
            
            // 查询排放情况
            List<ServiceDischargeInfo> dischargeInfoList = dischargeInfoMapper.selectServiceDischargeInfoByServiceAreaId(serviceAreaId);
            result.put("dischargeInfo", dischargeInfoList);
            
            // 查询系统整体评价
            List<ServiceSystemEvaluation> systemEvaluationList = systemEvaluationMapper.selectServiceSystemEvaluationByServiceAreaId(serviceAreaId);
            result.put("systemEvaluation", systemEvaluationList);
            
            // 查询控制系统信息
            List<ServiceControlSystem> controlSystemList = controlSystemMapper.selectServiceControlSystemByServiceAreaId(serviceAreaId);
            result.put("controlSystem", controlSystemList);
        } else {
            // 初始化所有空列表
            result.put("baseInfo", new ArrayList<>());
            result.put("waterUsage", new ArrayList<>());
            result.put("pipelineNetwork", new ArrayList<>());
            result.put("septicTank", new ArrayList<>());
            result.put("greaseTrap", new ArrayList<>());
            result.put("wastewaterStation", new ArrayList<>());
            result.put("equipment", new ArrayList<>());
            result.put("biochemicalSystem", new ArrayList<>());
            result.put("secondarySettlingTank", new ArrayList<>());
            result.put("otherTreatmentUnits", new ArrayList<>());
            result.put("dischargeInfo", new ArrayList<>());
            result.put("systemEvaluation", new ArrayList<>());
            result.put("controlSystem", new ArrayList<>());
        }
        
        return result;
    }

    /**
     * 导出服务区/收费站基本信息列表
     */
    @Override
    public void exportServiceBaseInfo(HttpServletResponse response, ServiceBaseInfo baseInfo) {
        List<ServiceBaseInfo> list = baseInfoMapper.selectServiceBaseInfoList(baseInfo);
        ExcelUtil<ServiceBaseInfo> util = new ExcelUtil<>(ServiceBaseInfo.class);
        util.exportExcel(response, list, "服务区/收费站基本信息数据");
    }

    /**
     * 下载导入模板
     */
    @Override
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ServiceBaseInfo> util = new ExcelUtil<>(ServiceBaseInfo.class);
        util.importTemplateExcel(response, "污水处理设施数据模板");
    }

    /**
     * 导入污水处理设施数据 - 支持垂直布局的单表格式
     *
     * @param inputStream Excel文件输入流
     * @param facilityName 设施名称
     * @return 导入结果信息
     */
    @Override
    @Transactional
    public String importWastewaterFacilityData(InputStream inputStream, String facilityName) {
        try {
            Workbook workbook = WorkbookFactory.create(inputStream);
            
            // 查询或创建基本信息
            ServiceBaseInfo baseInfo = baseInfoMapper.selectServiceBaseInfoByName(facilityName);
            if (baseInfo == null) {
                baseInfo = new ServiceBaseInfo();
                baseInfo.setFacilityName(facilityName);
                baseInfo.setFacilityType(1); // 默认为服务区
                baseInfo.setCreateTime(new Date());
                baseInfoMapper.insertServiceBaseInfo(baseInfo);
            }
            
            Long serviceAreaId = baseInfo.getId();
            int totalImported = 0;
            
            // 处理第一个工作表（通常包含所有数据）
            Sheet sheet = workbook.getSheetAt(0);
            log.info("正在处理工作表: {} (行数: {})", sheet.getSheetName(), sheet.getLastRowNum());
            
            // 识别数据模块并按区域导入
            List<DataModule> dataModules = identifyDataModules(sheet);
            log.info("识别到 {} 个数据模块", dataModules.size());
            
            for (DataModule module : dataModules) {
                log.info("处理模块: {} (第{}-{}行)", module.getType(), module.getStartRow(), module.getEndRow());
                
                switch (module.getType()) {
                    case "base_info":
                        totalImported += importBaseInfoFromModule(sheet, module, baseInfo);
                        break;
                    case "water_usage":
                        totalImported += importWaterUsageFromModule(sheet, module, serviceAreaId);
                        break;
                    case "pipeline_network":
                        totalImported += importPipelineNetworkFromModule(sheet, module, serviceAreaId);
                        break;
                    case "septic_tank":
                        totalImported += importSepticTankFromModule(sheet, module, serviceAreaId);
                        break;
                    case "grease_trap":
                        totalImported += importGreaseTrapFromModule(sheet, module, serviceAreaId);
                        break;
                    case "wastewater_station":
                        totalImported += importWastewaterStationFromModule(sheet, module, serviceAreaId);
                        break;
                    case "equipment_info":
                        totalImported += importEquipmentInfoFromModule(sheet, module, serviceAreaId);
                        break;
                    case "biochemical_system":
                        totalImported += importBiochemicalSystemInfoFromModule(sheet, module, serviceAreaId);
                        break;
                    case "secondary_settling_tank":
                        totalImported += importSecondarySettlingTankInfoFromModule(sheet, module, serviceAreaId);
                        break;
                    case "other_treatment_units":
                        totalImported += importOtherTreatmentUnitsFromModule(sheet, module, serviceAreaId);
                        break;
                    case "discharge_info":
                        totalImported += importDischargeInfoFromModule(sheet, module, serviceAreaId);
                        break;
                    case "system_evaluation":
                        totalImported += importSystemEvaluationFromModule(sheet, module, serviceAreaId);
                        break;
                    case "control_system":
                        totalImported += importControlSystemFromModule(sheet, module, serviceAreaId);
                        break;
                    default:
                        log.warn("未识别的模块类型: {}", module.getType());
                        break;
                }
            }
            
            return String.format("成功导入 %d 条数据，处理了 %d 个数据模块", totalImported, dataModules.size());
            
        } catch (Exception e) {
            log.error("导入污水处理设施数据失败", e);
            throw new RuntimeException("导入失败: " + e.getMessage());
        }
    }
    
    /**
     * 数据模块类
     */
    private static class DataModule {
        private String type;
        private String title;
        private int startRow;
        private int endRow;
        private List<String> headers;
        
        public DataModule(String type, String title, int startRow, int endRow) {
            this.type = type;
            this.title = title;
            this.startRow = startRow;
            this.endRow = endRow;
            this.headers = new ArrayList<>();
        }
        
        // Getters and setters
        public String getType() { return type; }
        public String getTitle() { return title; }
        public int getStartRow() { return startRow; }
        public int getEndRow() { return endRow; }
        public List<String> getHeaders() { return headers; }
        public void setHeaders(List<String> headers) { this.headers = headers; }
    }
    
    /**
     * 识别数据模块
     */
    private List<DataModule> identifyDataModules(Sheet sheet) {
        List<DataModule> modules = new ArrayList<>();
        DataModule currentModule = null;
        
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            List<String> rowData = getRowData(row);
            if (rowData.isEmpty()) continue;
            
            // 检测模块标题
            String moduleType = detectModuleType(rowData);
            if (moduleType != null) {
                // 保存上一个模块
                if (currentModule != null) {
                    currentModule.endRow = i - 1;
                    modules.add(currentModule);
                }
                
                // 开始新模块
                currentModule = new DataModule(moduleType, rowData.get(0), i, i);
                log.debug("发现模块: {} (第{}行)", moduleType, i + 1);
            } else if (currentModule != null) {
                // 检测表头
                if (isHeaderRow(rowData) && currentModule.getHeaders().isEmpty()) {
                    currentModule.setHeaders(rowData);
                }
                currentModule.endRow = i;
            }
        }
        
        // 添加最后一个模块
        if (currentModule != null) {
            modules.add(currentModule);
        }
        
        return modules;
    }
    
    /**
     * 检测是否为表头行
     */
    private boolean isHeaderRow(List<String> rowData) {
        if (rowData.isEmpty()) return false;
        
        String text = String.join(" ", rowData).toLowerCase();
        
        // 检测常见的表头关键词
        return text.contains("类型") || text.contains("名称") || text.contains("状态") || 
               text.contains("位置") || text.contains("数量") || text.contains("规格") ||
               text.contains("材质") || text.contains("工艺") || text.contains("容量") ||
               text.contains("参数") || text.contains("厂家") || text.contains("型号");
    }
    
    /**
     * 检测模块类型
     */
    private String detectModuleType(List<String> rowData) {
        if (rowData.isEmpty()) return null;
        
        String text = String.join(" ", rowData).toLowerCase();
        
        // 基于Python分析结果的模块识别规则
        if (text.contains("基本情况") && !text.contains("污水站")) {
            return "base_info";
        } else if (text.contains("用水情况") || text.contains("水源")) {
            return "water_usage";
        } else if (text.contains("管网") && !text.contains("排水")) {
            return "pipeline_network";
        } else if (text.contains("化粪池")) {
            return "septic_tank";
        } else if (text.contains("隔油池")) {
            return "grease_trap";
        } else if (text.contains("污水站") && text.contains("基本")) {
            return "wastewater_station";
        } else if (text.contains("主要设备") && text.contains("控制系统")) {
            return "control_system";
        } else if (text.contains("主要设备") || (text.contains("设备") && text.contains("控制"))) {
            return "equipment_info";
        } else if (text.contains("生化系统")) {
            return "biochemical_system";
        } else if (text.contains("二沉池")) {
            return "secondary_settling_tank";
        } else if (text.contains("其他处理") && text.contains("单元")) {
            return "other_treatment_units";
        } else if (text.contains("排放情况")) {
            return "discharge_info";
        } else if (text.contains("系统整体评价") || text.contains("环保投诉")) {
            return "system_evaluation";
        }
        
        return null;
    }
    
    /**
     * 获取行数据
     */
    private List<String> getRowData(Row row) {
        List<String> data = new ArrayList<>();
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            String value = getCellStringValue(cell);
            data.add(value != null ? value : "");
        }
        return data;
    }

    /**
     * 从模块导入基本信息
     */
    private int importBaseInfoFromModule(Sheet sheet, DataModule module, ServiceBaseInfo baseInfo) {
        int imported = 0;
        try {
            // 读取模块范围内的数据
            for (int i = module.getStartRow(); i <= module.getEndRow(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                List<String> rowData = getRowData(row);
                if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
                
                // 第3行格式: ["基本情况", "服务区", "长乐", "分公司", "恩施", "所在高速", "G5012利万高速"]
                if (i == 2) { // 基于分析结果的具体行号
                    baseInfo.setFacilityName(rowData.get(2)); // 长乐
                    baseInfo.setBranchCompany(rowData.get(4)); // 恩施
                    baseInfo.setHighwayName(rowData.get(6)); // G5012利万高速
                }
                // 第4行格式: ["", "建成时间", "2017", "互通", "有", "建筑物构成", "办公楼、宿舍、加油站、餐厅、公共卫生间"]
                else if (i == 3) {
                    try {
                        baseInfo.setConstructionYear(Integer.parseInt(rowData.get(2)));
                    } catch (NumberFormatException e) {
                        log.warn("建成时间格式错误: {}", rowData.get(2));
                    }
                    baseInfo.setBuildingComposition(rowData.get(6));
                }
                baseInfo.setUpdateTime(new Date());
                baseInfoMapper.updateServiceBaseInfo(baseInfo);
                imported++;
            }
        } catch (Exception e) {
            log.error("从模块导入基本信息失败", e);
        }
        return imported;
    }
    
    /**
     * 从模块导入用水情况
     */
    private int importWaterUsageFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        // 先删除已有数据
        waterUsageMapper.deleteServiceWaterUsageByServiceAreaId(serviceAreaId);
        // 读取模块范围内的数据
        for (int i = module.getStartRow(); i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            List<String> rowData = getRowData(row);
            if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
            // 第5行格式: ["用水情况", "水源", "自来水", "用水量\n（高峰期）", "60", "用水量\n（日常）", "200"]
            if (rowData.size() >= 7 && rowData.get(0).equals("用水情况")) {
                ServiceWaterUsage waterUsage = new ServiceWaterUsage();
                waterUsage.setServiceAreaId(serviceAreaId);
                waterUsage.setWaterSource(rowData.get(2)); // 自来水
                try {
                    waterUsage.setPeakUsage(new BigDecimal(rowData.get(4))); // 60
                    waterUsage.setDailyUsage(new BigDecimal(rowData.get(6))); // 200
                } catch (NumberFormatException e) {
                    log.warn("用水量数据格式错误: 高峰期={}, 日常={}", rowData.get(4), rowData.get(6));
                }

                waterUsage.setCreateTime(new Date());
                waterUsageMapper.insertServiceWaterUsage(waterUsage);
                imported++;
            }
        }
        return imported;
    }
    
    /**
     * 从模块导入管网信息
     */
    private int importPipelineNetworkFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        // 先删除已有数据
        pipelineNetworkMapper.deleteServicePipelineNetworkByServiceAreaId(serviceAreaId);
        // 读取模块范围内的数据行
        for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            List<String> rowData = getRowData(row);
            if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
            // 跳过表头行
            if (rowData.get(0).equals("管网")) continue;
            // 数据行格式: ["", "东区", "雨污分流", "300", "HDPE双壁波纹管", "完好通畅", ""]
            if (rowData.size() >= 6 && !rowData.get(1).isEmpty()) {
                ServicePipelineNetwork pipelineNetwork = new ServicePipelineNetwork();
                pipelineNetwork.setServiceAreaId(serviceAreaId);
                pipelineNetwork.setAreaType(rowData.get(1)); // 东区/西区
                pipelineNetwork.setRainSewageSeparation(rowData.get(2)); // 雨污分流
                try {
                    pipelineNetwork.setPipeDiameter(Integer.parseInt(rowData.get(3))); // 300
                } catch (NumberFormatException e) {
                    log.warn("管径数据格式错误: {}", rowData.get(3));
                }
                pipelineNetwork.setPipeMaterial(rowData.get(4)); // HDPE双壁波纹管
                pipelineNetwork.setOperationStatus(rowData.get(5)); // 完好通畅
                pipelineNetwork.setCreateTime(new Date());
                pipelineNetworkMapper.insertServicePipelineNetwork(pipelineNetwork);
                imported++;
            }
        }
        return imported;
    }
    
    /**
     * 从模块导入污水站信息
     */
    private int importWastewaterStationFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        try {
            // 先删除已有数据
            wastewaterStationMapper.deleteServiceWastewaterStationByServiceAreaId(serviceAreaId);
            
            // 读取模块范围内的数据
            for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                List<String> rowData = getRowData(row);
                if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
                
                // 数据行格式: ["", "东区", "MABR", "200", "碳钢", "2021", "正常运行"]
                if (rowData.size() >= 7 && !rowData.get(1).isEmpty()) {
                    ServiceWastewaterStation station = new ServiceWastewaterStation();
                    station.setServiceAreaId(serviceAreaId);
                    station.setAreaType(rowData.get(1)); // 东区
                    station.setTreatmentProcess(rowData.get(2)); // MABR
                    
                    try {
                        station.setDesignCapacity(new BigDecimal(rowData.get(3))); // 200
                    } catch (NumberFormatException e) {
                        log.warn("设计处理规模数据格式错误: {}", rowData.get(3));
                    }
                    
                    station.setStructureMaterial(rowData.get(4)); // 碳钢
                    station.setConstructionDate(rowData.get(5)); // 2021
                    station.setOperationStatus(rowData.get(6)); // 正常运行
                    station.setCreateTime(new Date());
                    
                    wastewaterStationMapper.insertServiceWastewaterStation(station);
                    imported++;
                }
            }
        } catch (Exception e) {
            log.error("从模块导入污水站信息失败", e);
        }
        return imported;
    }
    
    /**
     * 从模块导入设备信息
     */
    private int importEquipmentInfoFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        String currentEquipmentCategory = null;

        // 读取模块范围内的数据
        for (int i = module.getStartRow(); i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            List<String> rowData = getRowData(row);
            if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;

            // 检查第一列是否有非空值
            String firstColumnValue = rowData.get(0);
            if (firstColumnValue != null && !firstColumnValue.trim().isEmpty()) {
                // 检查是否包含"主要设备"
                if (firstColumnValue.contains("主要设备")) {
                    // 提取括号内的设备类型
                    String extractedType = extractEquipmentTypeFromParentheses(firstColumnValue);
                    if (extractedType != null && !extractedType.equals(currentEquipmentCategory)) {
                        // 发现新的设备类型
                        currentEquipmentCategory = extractedType;
                        log.debug("发现新设备类型: {}", currentEquipmentCategory);
                    }
                    // 跳过设备类型标题行，不作为数据处理
                    continue;
                }
            }
            // 处理设备数据行
            if (currentEquipmentCategory != null && !rowData.get(1).isEmpty()) {
                ServiceEquipmentInfo equipment = new ServiceEquipmentInfo();
                equipment.setServiceAreaId(serviceAreaId);
                equipment.setAreaType(rowData.get(1)); // 区域
                equipment.setEquipmentCategory(currentEquipmentCategory); // 使用当前设备类型
                equipment.setLocation(rowData.get(3)); // 位置
                // 尝试解析数量
                try {
                    if (rowData.size() > 4 && !rowData.get(4).isEmpty()) {
                        equipment.setQuantity(Integer.parseInt(rowData.get(4)));
                    }
                } catch (NumberFormatException e) {
                    equipment.setQuantity(1); // 默认数量为1
                }
                // 厂家型号和设备参数
                if (rowData.size() > 5) {
                    equipment.setManufacturerModel(rowData.get(5));
                }
                if (rowData.size() > 6) {
                    equipment.setEquipmentParameters(rowData.get(6));
                }
                // 设置设备子类型（如果第2列有值）
                if (rowData.size() > 2 && !rowData.get(2).isEmpty()) {
                    equipment.setEquipmentType(rowData.get(2));
                }
                equipment.setOperationStatus("正常运行"); // 默认状态
                equipment.setCreateTime(new Date());
                equipmentInfoMapper.insertServiceEquipmentInfo(equipment);
                imported++;
            }
        }
        return imported;
    }
    
    /**
     * 从括号中提取设备类型
     * 例如: "主要设备(格栅)" -> "格栅"
     */
    private String extractEquipmentTypeFromParentheses(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        
        // 处理英文括号
        int startIndex = text.indexOf('(');
        int endIndex = text.indexOf(')', startIndex);
        
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String result = text.substring(startIndex + 1, endIndex).trim();
            // 清理可能残留的中文括号
            return result.replace("（", "").replace("）", "").trim();
        }
        
        // 处理中文括号
        startIndex = text.indexOf('（');
        endIndex = text.indexOf('）', startIndex);
        
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String result = text.substring(startIndex + 1, endIndex).trim();
            // 清理可能残留的英文括号
            return result.replace("(", "").replace(")", "").trim();
        }
        
        // 如果没有括号，尝试提取"主要设备"后面的内容
        if (text.contains("主要设备")) {
            String remaining = text.substring(text.indexOf("主要设备") + 4).trim();
            // 清理所有类型的括号
            remaining = remaining.replace("(", "").replace(")", "")
                              .replace("（", "").replace("）", "").trim();
            return remaining.isEmpty() ? null : remaining;
        }
        
        return null;
    }
    
    /**
     * 从模块导入生化系统信息
     */
    private int importBiochemicalSystemInfoFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        try {
            // 先删除已有数据
            biochemicalSystemInfoMapper.deleteServiceBiochemicalSystemInfoByServiceAreaId(serviceAreaId);
            
            // 读取模块范围内的数据
            for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                List<String> rowData = getRowData(row);
                if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
                
                // 数据行格式: ["", "东区", "MABR膜卷", "正常且有污泥挂膜", "", "有，活性污泥充足且活性高", "有且正常运行"]
                if (rowData.size() >= 7 && !rowData.get(1).isEmpty()) {
                    ServiceBiochemicalSystemInfo biochemicalSystem = new ServiceBiochemicalSystemInfo();
                    biochemicalSystem.setServiceAreaId(serviceAreaId);
                    biochemicalSystem.setAreaType(rowData.get(1)); // 东区
                    biochemicalSystem.setFillerType(rowData.get(2)); // MABR膜卷
                    biochemicalSystem.setFillerStatus(rowData.get(3)); // 正常且有污泥挂膜
                    biochemicalSystem.setProcessParameters(rowData.get(4)); // 工艺参数
                    biochemicalSystem.setSludgeConcentration(rowData.get(5)); // 活性污泥浓度
                    biochemicalSystem.setNitrificationReflux(rowData.get(6)); // 硝化液回流
                    biochemicalSystem.setCreateTime(new Date());
                    
                    biochemicalSystemInfoMapper.insertServiceBiochemicalSystemInfo(biochemicalSystem);
                    imported++;
                }
            }
        } catch (Exception e) {
            log.error("从模块导入生化系统信息失败", e);
        }
        return imported;
    }
    
    /**
     * 从模块导入二沉池信息
     */
    private int importSecondarySettlingTankInfoFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        try {
            // 先删除已有数据
            secondarySettlingTankInfoMapper.deleteServiceSecondarySettlingTankInfoByServiceAreaId(serviceAreaId);
            
            // 读取模块范围内的数据
            for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                List<String> rowData = getRowData(row);
                if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
                
                // 数据行格式: ["", "东区", "无", "正常", "", "有且正常运行", "无且泥水分离效果好"]
                if (rowData.size() >= 7 && !rowData.get(1).isEmpty()) {
                    ServiceSecondarySettlingTankInfo secondarySettlingTank = new ServiceSecondarySettlingTankInfo();
                    secondarySettlingTank.setServiceAreaId(serviceAreaId);
                    secondarySettlingTank.setAreaType(rowData.get(1)); // 东区
                    secondarySettlingTank.setFillerType(rowData.get(2)); // 无
                    secondarySettlingTank.setFillerStatus(rowData.get(3)); // 正常
                    secondarySettlingTank.setProcessParameters(rowData.get(4)); // 工艺参数
                    secondarySettlingTank.setSludgeRefluxStatus(rowData.get(5)); // 污泥回流状态
                    secondarySettlingTank.setFloatingSludgeStatus(rowData.get(6)); // 浮泥状态
                    secondarySettlingTank.setCreateTime(new Date());
                    
                    secondarySettlingTankInfoMapper.insertServiceSecondarySettlingTankInfo(secondarySettlingTank);
                    imported++;
                }
            }
        } catch (Exception e) {
            log.error("从模块导入二沉池信息失败", e);
        }
        return imported;
    }
    
    /**
     * 从模块导入排放情况信息
     */
    private int importDischargeInfoFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        try {
            // 先删除已有数据
            dischargeInfoMapper.deleteServiceDischargeInfoByServiceAreaId(serviceAreaId);
            
            // 读取模块范围内的数据
            for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                List<String> rowData = getRowData(row);
                if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
                
                // 数据行格式: ["", "东区", "", "灌溉渠或农田", "农田", "", ""]
                if (rowData.size() >= 5 && !rowData.get(1).isEmpty()) {
                    ServiceDischargeInfo dischargeInfo = new ServiceDischargeInfo();
                    dischargeInfo.setServiceAreaId(serviceAreaId);
                    dischargeInfo.setAreaType(rowData.get(1)); // 东区
                    dischargeInfo.setOutletLocation(rowData.get(2)); // 排口位置
                    dischargeInfo.setDischargeDestination(rowData.get(3)); // 排放去向
                    dischargeInfo.setExternalEnvironment(rowData.get(4)); // 外部环境
                    if (rowData.size() > 5) {
                        dischargeInfo.setOtherConditions(rowData.get(5)); // 其他情况
                    }
                    dischargeInfo.setCreateTime(new Date());
                    
                    dischargeInfoMapper.insertServiceDischargeInfo(dischargeInfo);
                    imported++;
                }
            }
        } catch (Exception e) {
            log.error("从模块导入排放情况信息失败", e);
        }
        return imported;
    }
    
    /**
     * 从模块导入系统整体评价信息
     */
    private int importSystemEvaluationFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        try {
            // 先删除已有数据
            systemEvaluationMapper.deleteServiceSystemEvaluationByServiceAreaId(serviceAreaId);
            
            // 读取模块范围内的数据
            for (int i = module.getStartRow(); i <= module.getEndRow(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                List<String> rowData = getRowData(row);
                if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
                
                // 系统评价数据格式: ["系统整体评价", "系统运行正常，出水水质较好", "", "", "", "", ""]
                // 或: ["环保投诉情况及原因", "无", "", "", "", "", ""]
                if (rowData.size() >= 2 && !rowData.get(0).isEmpty()) {
                    ServiceSystemEvaluation systemEvaluation = new ServiceSystemEvaluation();
                    systemEvaluation.setServiceAreaId(serviceAreaId);
                    systemEvaluation.setEvaluationContent(rowData.get(0) + ": " + rowData.get(1));
                    systemEvaluation.setEvaluationDate(new Date());
                    systemEvaluation.setEvaluator("系统导入");
                    systemEvaluation.setCreateTime(new Date());
                    
                    systemEvaluationMapper.insertServiceSystemEvaluation(systemEvaluation);
                    imported++;
                }
            }
        } catch (Exception e) {
            log.error("从模块导入系统整体评价信息失败", e);
        }
        return imported;
    }
    
    /**
     * 占位方法 - 其他模块导入
     */
    private int importSepticTankFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        // 先删除已有的化粪池数据
        septicTankMapper.deleteServiceSepticTankByServiceAreaIdAndType(serviceAreaId, "化粪池");
        
        // 读取模块范围内的数据
        for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            List<String> rowData = getRowData(row);
            if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
            
            // 跳过表头行
            if (rowData.get(0).equals("化粪池")) continue;
            
            // 数据行格式: ["", "东区", "尺寸容积", "材质", "淤积情况", "废水接入情况", "其他情况"]
            if (rowData.size() >= 6 && !rowData.get(1).isEmpty()) {
                ServiceSepticTank septicTank = new ServiceSepticTank();
                septicTank.setServiceAreaId(serviceAreaId);
                septicTank.setAreaType(rowData.get(1)); // 区域
                septicTank.setType("化粪池"); // 设置类型为化粪池
                septicTank.setSizeVolume(rowData.get(2)); // 尺寸容积
                septicTank.setMaterial(rowData.get(3)); // 材质
                septicTank.setSiltationStatus(rowData.get(4)); // 淤积情况
                septicTank.setWastewaterAccess(rowData.get(5)); // 废水接入情况
                
                if (rowData.size() > 6) {
                    septicTank.setOtherConditions(rowData.get(6)); // 其他情况
                }
                
                septicTank.setCreateTime(new Date());
                septicTankMapper.insertServiceSepticTank(septicTank);
                imported++;
            }
        }
        return imported;
    }
    
    private int importGreaseTrapFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        int imported = 0;
        // 先删除已有的隔油池数据
        septicTankMapper.deleteServiceSepticTankByServiceAreaIdAndType(serviceAreaId, "隔油池");
        
        // 读取模块范围内的数据
        for (int i = module.getStartRow() + 1; i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            List<String> rowData = getRowData(row);
            if (rowData.isEmpty() || rowData.stream().allMatch(String::isEmpty)) continue;
            
            // 跳过表头行
            if (rowData.get(0).equals("隔油池")) continue;
            
            // 数据行格式: ["", "东区", "尺寸容积", "材质", "淤积情况", "废水接入情况", "其他情况"]
            if (rowData.size() >= 6 && !rowData.get(1).isEmpty()) {
                ServiceSepticTank septicTank = new ServiceSepticTank(); // 使用统一的ServiceSepticTank实体
                septicTank.setServiceAreaId(serviceAreaId);
                septicTank.setAreaType(rowData.get(1)); // 区域
                septicTank.setType("隔油池"); // 设置类型为隔油池
                septicTank.setSizeVolume(rowData.get(2)); // 尺寸容积
                septicTank.setMaterial(rowData.get(3)); // 材质
                septicTank.setSiltationStatus(rowData.get(4)); // 淤积情况
                septicTank.setWastewaterAccess(rowData.get(5)); // 废水接入情况
                
                if (rowData.size() > 6) {
                    septicTank.setOtherConditions(rowData.get(6)); // 其他情况
                }
                
                septicTank.setCreateTime(new Date());
                septicTankMapper.insertServiceSepticTank(septicTank); // 使用统一的mapper
                imported++;
            }
        }
        return imported;
    }
    
    /**
     * 从模块导入其他处理单元信息
     */
    private int importOtherTreatmentUnitsFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        log.info("开始导入其他处理单元信息，模块范围：第{}-{}行", module.getStartRow(), module.getEndRow());
        
        // 先删除该服务区域的现有其他处理单元数据
        otherTreatmentUnitsMapper.deleteServiceOtherTreatmentUnitsByServiceAreaId(serviceAreaId);
        
        int importedCount = 0;
        
        // 读取模块范围内的数据
        for (int i = module.getStartRow(); i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            // 跳过标题行和空行
            Cell firstCell = row.getCell(0);
            String firstCellValue = getCellStringValue(firstCell);
            
            // 跳过包含"其他处理"或标题行的行
            if (!firstCellValue.isEmpty() && (firstCellValue.contains("其他处理") || firstCellValue.contains("单元") ||
                firstCellValue.contains("区域") || firstCellValue.equals("区域"))) {
                continue;
            }
            
            try {
                ServiceOtherTreatmentUnits otherUnit = new ServiceOtherTreatmentUnits();
                otherUnit.setServiceAreaId(serviceAreaId);
                otherUnit.setCreateTime(new Date());
                otherUnit.setUpdateTime(new Date());
                
                // 映射Excel列到实体字段
                // 根据图片显示的表格结构：区域、处理单元名称、运行状态、对应设备名称、厂家型号、设备参数
                otherUnit.setAreaType(getCellStringValue(row.getCell(1))); // 区域
                otherUnit.setUnitName(getCellStringValue(row.getCell(2))); // 处理单元名称
                otherUnit.setOperationStatus(getCellStringValue(row.getCell(3))); // 运行状态
                otherUnit.setEquipmentName(getCellStringValue(row.getCell(4))); // 对应设备名称
                otherUnit.setManufacturerModel(getCellStringValue(row.getCell(5))); // 厂家型号
                otherUnit.setEquipmentParameters(getCellStringValue(row.getCell(6))); // 设备参数
                
                // 只有当至少有一个字段不为空时才插入记录
                if (isNotEmpty(otherUnit.getAreaType()) || isNotEmpty(otherUnit.getUnitName()) ||
                    isNotEmpty(otherUnit.getOperationStatus()) || isNotEmpty(otherUnit.getEquipmentName()) ||
                    isNotEmpty(otherUnit.getManufacturerModel()) || isNotEmpty(otherUnit.getEquipmentParameters())) {
                    
                    otherTreatmentUnitsMapper.insertServiceOtherTreatmentUnits(otherUnit);
                    importedCount++;
                    log.debug("成功导入其他处理单元记录：区域={}, 单元名称={}", otherUnit.getAreaType(), otherUnit.getUnitName());
                }
                
            } catch (Exception e) {
                log.error("导入第{}行其他处理单元数据时发生错误: {}", i + 1, e.getMessage());
            }
        }
        
        log.info("其他处理单元信息导入完成，共导入{}条记录", importedCount);
        return importedCount;
    }





    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) return "";
        
        try {
            switch (cell.getCellType()) {
                case STRING:
                    String stringValue = cell.getStringCellValue();
                    return stringValue != null ? stringValue.trim() : "";
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        return sdf.format(cell.getDateCellValue());
                    } else {
                        return String.valueOf((long) cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                case BLANK:
                    return "";
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格值时发生异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取单元格整数值
     */
    private Integer getCellIntValue(Cell cell) {
        if (cell == null) return null;
        
        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return (int) cell.getNumericCellValue();
                case STRING:
                    String str = cell.getStringCellValue().trim();
                    return str.isEmpty() ? null : Integer.parseInt(str);
                default:
                    return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取单元格BigDecimal值
     */
    private BigDecimal getCellBigDecimalValue(Cell cell) {
        if (cell == null) return null;
        
        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String str = cell.getStringCellValue().trim();
                    return str.isEmpty() ? null : new BigDecimal(str);
                default:
                    return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从模块导入控制系统信息
     */
    private int importControlSystemFromModule(Sheet sheet, DataModule module, Long serviceAreaId) {
        log.info("开始导入控制系统信息，模块范围：第{}-{}行", module.getStartRow(), module.getEndRow());
        
        // 先删除该服务区域的现有控制系统数据
        controlSystemMapper.deleteServiceControlSystemByServiceAreaId(serviceAreaId);
        
        int importedCount = 0;
        
        // 读取模块范围内的数据
        for (int i = module.getStartRow(); i <= module.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            // 跳过标题行和空行
            Cell firstCell = row.getCell(0);
            String firstCellValue = getCellStringValue(firstCell);
            // 跳过包含"主要设备"或"控制系统"的标题行
            if (!firstCellValue.isEmpty() && (firstCellValue.contains("主要设备") || firstCellValue.contains("控制系统") ||
                firstCellValue.contains("区域") || firstCellValue.equals("区域"))) {
                continue;
            }
            
            try {
                ServiceControlSystem controlSystem = new ServiceControlSystem();
                controlSystem.setServiceAreaId(serviceAreaId);
                controlSystem.setCreateTime(new Date());
                controlSystem.setUpdateTime(new Date());
                
                // 映射Excel列到实体字段
                // 根据图片显示的表格结构：区域、有无PLC、PLC运行状态、主要设备控制方式、主要电气元件、其他情况
                controlSystem.setArea(getCellStringValue(row.getCell(1))); // 区域
                controlSystem.setHasPlc(getCellStringValue(row.getCell(2))); // 有无PLC
                controlSystem.setPlcStatus(getCellStringValue(row.getCell(3))); // PLC运行状态
                controlSystem.setControlMethod(getCellStringValue(row.getCell(4))); // 主要设备控制方式
                controlSystem.setElectricalComponents(getCellStringValue(row.getCell(5))); // 主要电气元件
                controlSystem.setOtherSituation(getCellStringValue(row.getCell(6))); // 其他情况
                
                // 只有当至少有一个字段不为空时才插入记录
                if (isNotEmpty(controlSystem.getArea()) || isNotEmpty(controlSystem.getHasPlc()) ||
                    isNotEmpty(controlSystem.getPlcStatus()) || isNotEmpty(controlSystem.getControlMethod()) ||
                    isNotEmpty(controlSystem.getElectricalComponents()) || isNotEmpty(controlSystem.getOtherSituation())) {
                    
                    controlSystemMapper.insertServiceControlSystem(controlSystem);
                    importedCount++;
                    log.debug("成功导入控制系统记录：区域={}, PLC={}", controlSystem.getArea(), controlSystem.getHasPlc());
                }
                
            } catch (Exception e) {
                log.error("导入第{}行控制系统数据时发生错误: {}", i + 1, e.getMessage());
            }
        }
        
        log.info("控制系统信息导入完成，共导入{}条记录", importedCount);
        return importedCount;
    }

    /**
     * 获取服务区/收费站基本信息记录数
     */
    @Override
    public int countServiceBaseInfo(ServiceBaseInfo baseInfo) {
        return baseInfoMapper.countServiceBaseInfo(baseInfo);
    }
}
