package com.tunnel.service.impl;

import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.ServiceChangeLog;
import com.tunnel.mapper.ServiceChangeLogMapper;
import com.tunnel.service.ServiceChangeLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 服务区/收费站档案变更日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Service
public class ServiceChangeLogServiceImpl implements ServiceChangeLogService {
    
    @Autowired
    private ServiceChangeLogMapper serviceChangeLogMapper;

    /**
     * 查询服务区/收费站档案变更日志
     *
     * @param id 服务区/收费站档案变更日志主键
     * @return 服务区/收费站档案变更日志
     */
    @Override
    public ServiceChangeLog selectServiceChangeLogById(Long id) {
        return serviceChangeLogMapper.selectServiceChangeLogById(id);
    }

    /**
     * 查询服务区/收费站档案变更日志列表
     *
     * @param serviceChangeLog 服务区/收费站档案变更日志
     * @return 服务区/收费站档案变更日志
     */
    @Override
    public List<ServiceChangeLog> selectServiceChangeLogList(ServiceChangeLog serviceChangeLog) {
        return serviceChangeLogMapper.selectServiceChangeLogList(serviceChangeLog);
    }

    /**
     * 根据表名和记录ID查询变更日志列表
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 变更日志集合
     */
    @Override
    public List<ServiceChangeLog> selectServiceChangeLogByTableAndRecord(String tableName, String recordId) {
        return serviceChangeLogMapper.selectServiceChangeLogByTableAndRecord(tableName, recordId);
    }

    /**
     * 根据设施名称查询变更日志列表
     *
     * @param facilityName 设施名称
     * @return 变更日志集合
     */
    @Override
    public List<ServiceChangeLog> selectServiceChangeLogByFacilityName(String facilityName) {
        return serviceChangeLogMapper.selectServiceChangeLogByFacilityName(facilityName);
    }

    /**
     * 新增服务区/收费站档案变更日志
     *
     * @param serviceChangeLog 服务区/收费站档案变更日志
     * @return 结果
     */
    @Override
    public int insertServiceChangeLog(ServiceChangeLog serviceChangeLog) {
        serviceChangeLog.setCreateTime(new Date());
        return serviceChangeLogMapper.insertServiceChangeLog(serviceChangeLog);
    }

    /**
     * 修改服务区/收费站档案变更日志
     *
     * @param serviceChangeLog 服务区/收费站档案变更日志
     * @return 结果
     */
    @Override
    public int updateServiceChangeLog(ServiceChangeLog serviceChangeLog) {
        serviceChangeLog.setUpdateTime(new Date());
        return serviceChangeLogMapper.updateServiceChangeLog(serviceChangeLog);
    }

    /**
     * 批量删除服务区/收费站档案变更日志
     *
     * @param ids 需要删除的服务区/收费站档案变更日志主键
     * @return 结果
     */
    @Override
    public int deleteServiceChangeLogByIds(Long[] ids) {
        return serviceChangeLogMapper.deleteServiceChangeLogByIds(ids);
    }

    /**
     * 删除服务区/收费站档案变更日志信息
     *
     * @param id 服务区/收费站档案变更日志主键
     * @return 结果
     */
    @Override
    public int deleteServiceChangeLogById(Long id) {
        return serviceChangeLogMapper.deleteServiceChangeLogById(id);
    }

    /**
     * 记录数据变更日志
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param operationType 操作类型（INSERT/UPDATE/DELETE）
     * @param oldValues 变更前数据（JSON格式）
     * @param newValues 变更后数据（JSON格式）
     * @param remark 备注
     * @return 结果
     */
    @Override
    public int recordChangeLog(String tableName, String recordId, String operationType, 
                              String oldValues, String newValues, String remark) {
        Long operatorId = null;
        try {
            operatorId = SecurityUtils.getUserId();
        } catch (Exception e) {
            // 如果获取不到当前用户，使用默认值
        }
        return recordChangeLog(tableName, recordId, operationType, oldValues, newValues, remark, operatorId);
    }

    /**
     * 记录数据变更日志（带操作人）
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param operationType 操作类型（INSERT/UPDATE/DELETE）
     * @param oldValues 变更前数据（JSON格式）
     * @param newValues 变更后数据（JSON格式）
     * @param remark 备注
     * @param operatorId 操作人ID
     * @return 结果
     */
    @Override
    public int recordChangeLog(String tableName, String recordId, String operationType, 
                              String oldValues, String newValues, String remark, Long operatorId) {
        ServiceChangeLog changeLog = new ServiceChangeLog();
        changeLog.setTableName(tableName);
        changeLog.setRecordId(recordId);
        changeLog.setOperationType(operationType);
        changeLog.setOldValues(oldValues);
        changeLog.setNewValues(newValues);
        changeLog.setRemark(remark);
        changeLog.setCreator(operatorId);
        changeLog.setCreateTime(new Date());
        
        return serviceChangeLogMapper.insertServiceChangeLog(changeLog);
    }
}
