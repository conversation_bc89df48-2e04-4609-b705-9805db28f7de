package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.ServiceBaseInfo;
import com.tunnel.domain.ServiceChangeLog;
import com.tunnel.service.WastewaterFacilityService;
import com.tunnel.service.ServiceChangeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 污水处理设施管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/wastewater/facility")
@Api(tags = "污水处理设施管理")
public class WastewaterFacilityController extends BaseController {
    
    @Autowired
    private WastewaterFacilityService wastewaterFacilityService;

    @Autowired
    private ServiceChangeLogService serviceChangeLogService;

    /**
     * 查询服务区/收费站基本信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取服务区/收费站基本信息列表", notes = "获取全部服务区/收费站基本信息数据-分页")
    @Operation(summary = "获取服务区/收费站基本信息列表", description = "获取全部服务区/收费站基本信息数据-分页")
    public TableDataInfo list(ServiceBaseInfo baseInfo) {
        startPage();
        List<ServiceBaseInfo> list = wastewaterFacilityService.selectServiceBaseInfoList(baseInfo);
        return getDataTable(list);
    }

    /**
     * 获取服务区/收费站基本信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取服务区/收费站基本信息详细", notes = "根据ID获取服务区/收费站基本信息详情")
    @Operation(summary = "获取服务区/收费站基本信息详细", description = "根据ID获取服务区/收费站基本信息详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wastewaterFacilityService.selectServiceBaseInfoById(id));
    }

    /**
     * 根据设施名称获取完整的污水处理设施数据
     */
    @GetMapping("/getByFacilityName/{facilityName}")
    @ApiOperation(value = "根据设施名称获取完整数据", notes = "根据设施名称获取所有相关的污水处理设施数据")
    @Operation(summary = "根据设施名称获取完整数据", description = "根据设施名称获取所有相关的污水处理设施数据")
    @ApiImplicitParam(name = "facilityName", value = "设施名称", required = true, dataType = "String", paramType = "path")
    public AjaxResult getByFacilityName(@PathVariable("facilityName") String facilityName) {
        Map<String, Object> result = wastewaterFacilityService.selectAllDataByFacilityName(facilityName);
        return success(result);
    }

    /**
     * 新增服务区/收费站基本信息
     */
    @Log(title = "服务区/收费站基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增服务区/收费站基本信息", notes = "新增单条服务区/收费站基本信息")
    @Operation(summary = "新增服务区/收费站基本信息", description = "新增单条服务区/收费站基本信息")
    public AjaxResult add(@Validated @RequestBody ServiceBaseInfo baseInfo) {
        return toAjax(wastewaterFacilityService.insertServiceBaseInfo(baseInfo));
    }

    /**
     * 修改服务区/收费站基本信息
     */
    @Log(title = "服务区/收费站基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改服务区/收费站基本信息", notes = "修改服务区/收费站基本信息")
    @Operation(summary = "修改服务区/收费站基本信息", description = "修改服务区/收费站基本信息")
    public AjaxResult edit(@Validated @RequestBody ServiceBaseInfo baseInfo) {
        return toAjax(wastewaterFacilityService.updateServiceBaseInfo(baseInfo));
    }

    /**
     * 删除服务区/收费站基本信息
     */
    @Log(title = "服务区/收费站基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除服务区/收费站基本信息", notes = "批量删除服务区/收费站基本信息")
    @Operation(summary = "删除服务区/收费站基本信息", description = "批量删除服务区/收费站基本信息")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wastewaterFacilityService.deleteServiceBaseInfoByIds(ids));
    }

    /**
     * 导出服务区/收费站基本信息列表
     */
    @Log(title = "服务区/收费站基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出服务区/收费站基本信息", notes = "导出服务区/收费站基本信息到Excel")
    @Operation(summary = "导出服务区/收费站基本信息", description = "导出服务区/收费站基本信息到Excel")
    public void export(HttpServletResponse response, ServiceBaseInfo baseInfo) {
        wastewaterFacilityService.exportServiceBaseInfo(response, baseInfo);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载污水处理设施导入模板")
    public void importTemplate(HttpServletResponse response) {
        wastewaterFacilityService.importTemplate(response);
    }

    /**
     * 导入污水处理设施数据
     */
    @Log(title = "污水处理设施数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "导入污水处理设施数据", notes = "从Excel文件导入污水处理设施数据")
    @Operation(summary = "导入污水处理设施数据", description = "从Excel文件导入污水处理设施数据")
    public AjaxResult importData(@RequestParam("file") MultipartFile file, 
                                @RequestParam("facilityName") String facilityName) {
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }
            
            InputStream inputStream = file.getInputStream();
            String result = wastewaterFacilityService.importWastewaterFacilityData(inputStream, facilityName);
            
            if (result.startsWith("成功")) {
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error(result);
            }
        } catch (Exception e) {
            logger.error("导入污水处理设施数据失败", e);
            return AjaxResult.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取服务区/收费站基本信息记录数
     */
    @GetMapping("/count")
    @ApiOperation(value = "获取服务区/收费站基本信息记录数", notes = "获取服务区/收费站基本信息记录总数")
    @Operation(summary = "获取服务区/收费站基本信息记录数", description = "获取服务区/收费站基本信息记录总数")
    public AjaxResult count(ServiceBaseInfo baseInfo) {
        int count = wastewaterFacilityService.countServiceBaseInfo(baseInfo);
        return success(count);
    }

    /**
     * 根据设施名称查询档案变更日志
     */
    @GetMapping("/changeLog/{facilityName}")
    @ApiOperation(value = "查询档案变更日志", notes = "根据设施名称查询档案变更日志")
    @Operation(summary = "查询档案变更日志", description = "根据设施名称查询档案变更日志")
    @ApiImplicitParam(name = "facilityName", value = "设施名称", required = true, dataType = "String", paramType = "path")
    public TableDataInfo getChangeLog(@PathVariable("facilityName") String facilityName) {
        startPage();
        List<ServiceChangeLog> list = serviceChangeLogService.selectServiceChangeLogByFacilityName(facilityName);
        return getDataTable(list);
    }

    /**
     * 根据表名和记录ID查询档案变更日志
     */
    @GetMapping("/changeLog/{tableName}/{recordId}")
    @ApiOperation(value = "查询记录变更日志", notes = "根据表名和记录ID查询变更日志")
    @Operation(summary = "查询记录变更日志", description = "根据表名和记录ID查询变更日志")
    public TableDataInfo getChangeLogByRecord(@PathVariable("tableName") String tableName,
                                             @PathVariable("recordId") String recordId) {
        startPage();
        List<ServiceChangeLog> list = serviceChangeLogService.selectServiceChangeLogByTableAndRecord(tableName, recordId);
        return getDataTable(list);
    }
}
