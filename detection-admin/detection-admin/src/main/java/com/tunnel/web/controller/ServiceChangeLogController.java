package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.ServiceChangeLog;
import com.tunnel.service.ServiceChangeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务区/收费站档案变更日志Controller
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@RestController
@RequestMapping("/wastewater/changeLog")
@Api(tags = "服务区/收费站档案变更日志")
public class ServiceChangeLogController extends BaseController {
    
    @Autowired
    private ServiceChangeLogService serviceChangeLogService;

    /**
     * 查询服务区/收费站档案变更日志列表
     */
    @PreAuthorize("@ss.hasPermi('wastewater:changeLog:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取档案变更日志列表", notes = "获取全部档案变更日志数据-分页")
    @Operation(summary = "获取档案变更日志列表", description = "获取全部档案变更日志数据-分页")
    public TableDataInfo list(ServiceChangeLog serviceChangeLog) {
        startPage();
        List<ServiceChangeLog> list = serviceChangeLogService.selectServiceChangeLogList(serviceChangeLog);
        return getDataTable(list);
    }

    /**
     * 根据表名和记录ID查询变更日志列表
     */
    @PreAuthorize("@ss.hasPermi('wastewater:changeLog:list')")
    @GetMapping("/listByRecord/{tableName}/{recordId}")
    @ApiOperation(value = "根据记录查询变更日志", notes = "根据表名和记录ID查询变更日志列表")
    @Operation(summary = "根据记录查询变更日志", description = "根据表名和记录ID查询变更日志列表")
    public TableDataInfo listByRecord(@PathVariable("tableName") String tableName, 
                                     @PathVariable("recordId") String recordId) {
        startPage();
        List<ServiceChangeLog> list = serviceChangeLogService.selectServiceChangeLogByTableAndRecord(tableName, recordId);
        return getDataTable(list);
    }

    /**
     * 根据设施名称查询变更日志列表
     */
    @PreAuthorize("@ss.hasPermi('wastewater:changeLog:list')")
    @GetMapping("/listByFacility/{facilityName}")
    @ApiOperation(value = "根据设施名称查询变更日志", notes = "根据设施名称查询变更日志列表")
    @Operation(summary = "根据设施名称查询变更日志", description = "根据设施名称查询变更日志列表")
    @ApiImplicitParam(name = "facilityName", value = "设施名称", required = true, dataType = "String", paramType = "path")
    public TableDataInfo listByFacility(@PathVariable("facilityName") String facilityName) {
        startPage();
        List<ServiceChangeLog> list = serviceChangeLogService.selectServiceChangeLogByFacilityName(facilityName);
        return getDataTable(list);
    }

    /**
     * 导出服务区/收费站档案变更日志列表
     */
    @PreAuthorize("@ss.hasPermi('wastewater:changeLog:export')")
    @Log(title = "服务区/收费站档案变更日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出档案变更日志", notes = "导出档案变更日志到Excel")
    @Operation(summary = "导出档案变更日志", description = "导出档案变更日志到Excel")
    public void export(HttpServletResponse response, ServiceChangeLog serviceChangeLog) {
        List<ServiceChangeLog> list = serviceChangeLogService.selectServiceChangeLogList(serviceChangeLog);
        ExcelUtil<ServiceChangeLog> util = new ExcelUtil<ServiceChangeLog>(ServiceChangeLog.class);
        util.exportExcel(response, list, "档案变更日志");
    }

    /**
     * 获取服务区/收费站档案变更日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('wastewater:changeLog:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取档案变更日志详细", notes = "根据ID获取档案变更日志详情")
    @Operation(summary = "获取档案变更日志详细", description = "根据ID获取档案变更日志详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(serviceChangeLogService.selectServiceChangeLogById(id));
    }

    /**
     * 删除服务区/收费站档案变更日志
     */
    @PreAuthorize("@ss.hasPermi('wastewater:changeLog:remove')")
    @Log(title = "服务区/收费站档案变更日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除档案变更日志", notes = "批量删除档案变更日志")
    @Operation(summary = "删除档案变更日志", description = "批量删除档案变更日志")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(serviceChangeLogService.deleteServiceChangeLogByIds(ids));
    }
}
