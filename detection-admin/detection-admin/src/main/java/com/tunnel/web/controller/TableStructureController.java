package com.tunnel.web.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 表结构查询Controller
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@RestController
@RequestMapping("/system/tableStructure")
@Api(tags = "表结构查询")
public class TableStructureController extends BaseController {
    
    @Autowired
    private DataSource dataSource;

    /**
     * 获取表的字段信息和备注
     */
    @GetMapping("/fields/{tableName}")
    @ApiOperation(value = "获取表字段信息", notes = "获取指定表的字段名称和备注信息")
    @Operation(summary = "获取表字段信息", description = "获取指定表的字段名称和备注信息")
    public AjaxResult getTableFields(@PathVariable("tableName") String tableName) {
        try {
            Map<String, String> fieldComments = new HashMap<>();
            
            String sql = "SELECT COLUMN_NAME, COLUMN_COMMENT " +
                        "FROM INFORMATION_SCHEMA.COLUMNS " +
                        "WHERE TABLE_SCHEMA = DATABASE() " +
                        "AND TABLE_NAME = ? " +
                        "ORDER BY ORDINAL_POSITION";
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, tableName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        String columnName = rs.getString("COLUMN_NAME");
                        String columnComment = rs.getString("COLUMN_COMMENT");
                        
                        // 如果备注为空，使用字段名作为显示名
                        if (columnComment == null || columnComment.trim().isEmpty()) {
                            columnComment = columnName;
                        }
                        
                        fieldComments.put(columnName, columnComment);
                    }
                }
            }
            
            return success(fieldComments);
            
        } catch (Exception e) {
            logger.error("获取表字段信息失败: {}", e.getMessage(), e);
            return error("获取表字段信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取多个表的字段信息
     */
    @PostMapping("/batchFields")
    @ApiOperation(value = "批量获取表字段信息", notes = "批量获取多个表的字段名称和备注信息")
    @Operation(summary = "批量获取表字段信息", description = "批量获取多个表的字段名称和备注信息")
    public AjaxResult getBatchTableFields(@RequestBody String[] tableNames) {
        try {
            Map<String, Map<String, String>> result = new HashMap<>();
            
            for (String tableName : tableNames) {
                Map<String, String> fieldComments = new HashMap<>();
                
                String sql = "SELECT COLUMN_NAME, COLUMN_COMMENT " +
                            "FROM INFORMATION_SCHEMA.COLUMNS " +
                            "WHERE TABLE_SCHEMA = DATABASE() " +
                            "AND TABLE_NAME = ? " +
                            "ORDER BY ORDINAL_POSITION";
                
                try (Connection conn = dataSource.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {
                    
                    stmt.setString(1, tableName);
                    
                    try (ResultSet rs = stmt.executeQuery()) {
                        while (rs.next()) {
                            String columnName = rs.getString("COLUMN_NAME");
                            String columnComment = rs.getString("COLUMN_COMMENT");
                            
                            // 如果备注为空，使用字段名作为显示名
                            if (columnComment == null || columnComment.trim().isEmpty()) {
                                columnComment = columnName;
                            }
                            
                            fieldComments.put(columnName, columnComment);
                        }
                    }
                }
                
                result.put(tableName, fieldComments);
            }
            
            return success(result);
            
        } catch (Exception e) {
            logger.error("批量获取表字段信息失败: {}", e.getMessage(), e);
            return error("批量获取表字段信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取表的详细信息
     */
    @GetMapping("/info/{tableName}")
    @ApiOperation(value = "获取表详细信息", notes = "获取表的详细信息包括表名、备注等")
    @Operation(summary = "获取表详细信息", description = "获取表的详细信息包括表名、备注等")
    public AjaxResult getTableInfo(@PathVariable("tableName") String tableName) {
        try {
            Map<String, Object> tableInfo = new HashMap<>();
            
            String sql = "SELECT TABLE_NAME, TABLE_COMMENT, ENGINE, TABLE_COLLATION " +
                        "FROM INFORMATION_SCHEMA.TABLES " +
                        "WHERE TABLE_SCHEMA = DATABASE() " +
                        "AND TABLE_NAME = ?";
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, tableName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        tableInfo.put("tableName", rs.getString("TABLE_NAME"));
                        tableInfo.put("tableComment", rs.getString("TABLE_COMMENT"));
                        tableInfo.put("engine", rs.getString("ENGINE"));
                        tableInfo.put("collation", rs.getString("TABLE_COLLATION"));
                    }
                }
            }
            
            return success(tableInfo);
            
        } catch (Exception e) {
            logger.error("获取表详细信息失败: {}", e.getMessage(), e);
            return error("获取表详细信息失败: " + e.getMessage());
        }
    }
}
