# 服务区/收费站档案变更日志功能

## 功能概述

本功能为服务区/收费站档案信息提供了完整的变更日志记录和查询功能，可以追踪所有档案数据的增删改操作。

## 功能特性

1. **自动记录变更日志**：在档案信息发生增删改时自动记录变更日志
2. **Excel导入日志记录**：在Excel导入数据时记录数据覆盖的变更日志
3. **多维度查询**：支持按设施名称、表名、记录ID等多种方式查询变更日志
4. **详细变更信息**：记录变更前后的完整数据（JSON格式）

## 数据库表结构

### sc_service_change_log 表

```sql
CREATE TABLE `sc_service_change_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) NOT NULL COMMENT '变更的表名',
  `record_id` varchar(50) NOT NULL COMMENT '变更记录的主键ID',
  `operation_type` enum('INSERT','UPDATE','DELETE') NOT NULL COMMENT '操作类型：新增/修改/删除',
  `old_values` mediumtext COMMENT '变更前的完整记录（JSON格式）',
  `new_values` mediumtext COMMENT '变更后的完整记录（JSON格式）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` bigint DEFAULT NULL COMMENT '创建人',
  `modifier` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_table_record` (`table_name`,`record_id`),
  KEY `idx_table_operation` (`table_name`,`operation_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务区/收费站档案变更日志表';
```

## API接口

### 1. 查询变更日志列表

**接口地址**：`GET /wastewater/changeLog/list`

**参数**：
- `tableName`：表名（可选）
- `recordId`：记录ID（可选）
- `operationType`：操作类型（可选）
- `facilityName`：设施名称（可选）
- `beginTime`：开始时间（可选）
- `endTime`：结束时间（可选）

### 2. 根据设施名称查询变更日志

**接口地址**：`GET /wastewater/changeLog/listByFacility/{facilityName}`

**参数**：
- `facilityName`：设施名称

### 3. 根据表名和记录ID查询变更日志

**接口地址**：`GET /wastewater/changeLog/listByRecord/{tableName}/{recordId}`

**参数**：
- `tableName`：表名
- `recordId`：记录ID

### 4. 在污水处理设施控制器中的快捷查询

**接口地址**：`GET /wastewater/facility/changeLog/{facilityName}`

**参数**：
- `facilityName`：设施名称

## 前端API调用示例

```javascript
import { listChangeLogByFacility, listChangeLogByRecord } from '@/api/wastewater/changeLog'

// 根据设施名称查询变更日志
listChangeLogByFacility('长乐服务区', {
  pageNum: 1,
  pageSize: 10
}).then(response => {
  console.log('变更日志列表:', response.rows)
})

// 根据表名和记录ID查询变更日志
listChangeLogByRecord('sc_service_base_info', '1', {
  pageNum: 1,
  pageSize: 10
}).then(response => {
  console.log('记录变更日志:', response.rows)
})
```

## Excel导入时的日志记录

当通过Excel导入数据时，系统会自动记录以下变更日志：

1. **删除操作**：导入前删除已有数据时记录删除日志
2. **新增操作**：导入新数据时记录新增日志
3. **更新操作**：更新基本信息时记录更新日志

### 支持的表

- `sc_service_base_info`：基本信息
- `sc_service_water_usage`：用水情况
- `sc_service_pipeline_network`：管网信息
- `sc_service_wastewater_station`：污水处理站
- `sc_service_equipment_info`：设备信息
- `sc_service_septic_tank`：化粪池/隔油池
- 其他相关表...

## 使用场景

1. **数据审计**：追踪谁在什么时候修改了什么数据
2. **数据恢复**：通过变更日志了解数据的历史状态
3. **导入监控**：监控Excel导入时的数据变更情况
4. **合规要求**：满足数据变更追踪的合规要求

## 注意事项

1. 变更日志会占用一定的存储空间，建议定期清理历史日志
2. 大批量数据导入时会产生大量日志记录，可能影响性能
3. 日志记录是异步的，如果记录失败不会影响主业务流程
4. 建议在生产环境中配置适当的日志保留策略
