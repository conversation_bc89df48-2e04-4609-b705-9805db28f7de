# 变更日志功能故障排除指南

## 问题1：Parameter 'tableName' not found 错误

### 错误信息
```
org.apache.ibatis.binding.BindingException: Parameter 'tableName' not found. Available parameters are [arg1, arg0, param1, param2]
```

### 解决方案
已修复：修改了ServiceChangeLogServiceImpl中的selectServiceChangeLogByTableAndRecord方法，使用ServiceChangeLog对象作为查询参数，而不是直接传递多个参数。

### 修复内容
1. 在ServiceChangeLogServiceImpl中创建查询条件对象
2. 修改XML中tableName的查询条件从LIKE改为精确匹配

## 问题2：前端组件无法正常显示

### 可能原因
1. 组件路径错误
2. API接口调用失败
3. 权限问题

### 检查步骤
1. 确认ChangeLogDialog.vue组件已正确创建
2. 确认在主页面中正确引入了组件
3. 检查API接口是否正常响应
4. 检查用户是否有相应权限

## 问题3：数据库表不存在

### 解决方案
执行SQL脚本创建表：
```sql
-- 运行 detection-admin/sql/service_change_log.sql
```

## 问题4：日志记录不生效

### 可能原因
1. AOP切面未生效
2. ServiceChangeLogService注入失败
3. 数据库连接问题

### 检查步骤
1. 确认Spring AOP配置正确
2. 确认ServiceChangeLogService已正确注入
3. 检查数据库连接和权限

## 问题5：JSON数据显示异常

### 可能原因
1. JSON格式错误
2. 前端解析失败

### 解决方案
1. 检查后端JSON序列化
2. 确认前端formatJson方法正常工作

## 测试步骤

### 1. 数据库测试
```sql
-- 运行 detection-admin/sql/test_change_log.sql
```

### 2. 后端API测试
```bash
# 测试查询接口
curl -X GET "http://localhost:8080/wastewater/changeLog/listByRecord/sc_service_base_info/1"
```

### 3. 前端功能测试
1. 打开服务区列表页面
2. 点击"查看档案"
3. 点击任意模块的"查看日志记录"按钮
4. 验证日志列表是否正常显示
5. 点击任意日志记录查看详情

## 常见问题解决

### Q: 点击"查看日志记录"按钮没有反应
A: 检查浏览器控制台是否有JavaScript错误，确认API接口是否正常

### Q: 日志列表为空
A: 
1. 确认数据库中是否有相关日志数据
2. 检查查询条件是否正确
3. 确认表名和记录ID是否匹配

### Q: 变更详情显示异常
A: 
1. 检查JSON数据格式是否正确
2. 确认formatJson方法是否正常工作
3. 检查CSS样式是否正确加载

### Q: 权限错误
A: 
1. 确认用户有相应的查询权限
2. 检查Spring Security配置
3. 确认权限注解是否正确

## 日志级别调整

如需调试，可以在application.yml中添加：
```yaml
logging:
  level:
    com.tunnel.mapper.ServiceChangeLogMapper: DEBUG
    com.tunnel.service.impl.ServiceChangeLogServiceImpl: DEBUG
```

## 性能优化建议

1. 定期清理历史日志数据
2. 为常用查询字段添加索引
3. 考虑使用分页查询避免大量数据加载
4. 对于大批量操作，考虑异步记录日志
