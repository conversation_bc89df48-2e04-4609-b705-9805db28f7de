# 档案变更日志功能使用指南

## 功能概述

在"查看档案"弹窗中，每个模块都新增了"查看日志记录"按钮，点击后可以查看该模块的数据变更历史，包括变更前后的详细对比。

## 使用步骤

### 1. 进入档案查看页面
1. 在服务区/收费站列表页面，点击某个服务区的"查看档案"按钮
2. 弹出档案详情对话框

### 2. 查看模块变更日志
1. 在档案详情对话框中，每个模块（如基本信息、用水情况、管网系统等）的标题旁都有一个蓝色的"查看日志记录"按钮
2. 点击任意模块的"查看日志记录"按钮
3. 弹出该模块的变更日志对话框

### 3. 查询和筛选日志
在变更日志对话框中，您可以：
- **按操作类型筛选**：选择"新增"、"修改"或"删除"
- **按时间范围筛选**：选择开始时间和结束时间
- **查看日志列表**：显示所有符合条件的变更记录

### 4. 查看变更详情
1. 在日志列表中，点击任意一行记录
2. 或者点击"查看详情"按钮
3. 弹出变更详情对比对话框，显示：
   - **变更前数据**：左侧显示修改前的完整数据（JSON格式）
   - **变更后数据**：右侧显示修改后的完整数据（JSON格式）
   - **操作信息**：显示操作类型、操作人、操作时间、备注等

## 支持的模块

目前支持查看以下模块的变更日志：

### 基本信息模块
- **基本信息**：服务区名称、分公司、建成时间等
- **用水情况**：水源、用水量等信息

### 设施模块
- **管网系统**：管网信息、雨污分流情况等
- **污水处理设施**：污水站基本情况
- **主要设备**：格栅设备、提升泵等设备信息

### 处理单元模块
- **化粪池**：化粪池信息和运行状态
- **隔油池**：隔油池信息和运行状态
- **排放信息**：排口位置、排放去向等
- **系统评价**：评价内容、评价人、评价时间等

## 日志记录时机

系统会在以下情况自动记录变更日志：

### 1. Excel导入时
- **删除操作**：导入前删除已有数据时，记录被删除的数据
- **新增操作**：导入新数据时，记录新增的数据
- **更新操作**：更新基本信息时，记录更新前后的数据对比

### 2. 手动编辑时
- 通过系统界面手动添加、修改、删除档案信息时自动记录

## 日志信息说明

每条变更日志包含以下信息：

| 字段 | 说明 |
|------|------|
| 操作类型 | 新增(INSERT)、修改(UPDATE)、删除(DELETE) |
| 表名 | 数据库表名，如 sc_service_base_info |
| 记录ID | 被操作记录的主键ID |
| 操作人 | 执行操作的用户姓名 |
| 操作时间 | 操作发生的具体时间 |
| 变更前数据 | 修改前的完整数据（JSON格式） |
| 变更后数据 | 修改后的完整数据（JSON格式） |
| 备注 | 操作备注，如"Excel导入更新基本信息" |

## 使用场景

### 1. 数据审计
- 追踪谁在什么时候修改了什么数据
- 了解数据变更的完整历史

### 2. 问题排查
- 当发现数据异常时，可以查看变更历史找出问题原因
- 对比变更前后的数据差异

### 3. 导入监控
- 监控Excel导入时的数据变更情况
- 确认导入操作是否按预期执行

### 4. 数据恢复参考
- 通过变更日志了解数据的历史状态
- 为数据恢复提供参考信息

## 注意事项

1. **权限要求**：需要有相应的查询权限才能查看变更日志
2. **数据量**：大量的变更记录可能影响查询性能，建议使用时间范围筛选
3. **存储空间**：变更日志会占用一定的存储空间，建议定期清理历史日志
4. **JSON格式**：变更前后的数据以JSON格式存储，便于程序处理但可读性较差

## 技术实现

- **后端**：基于AOP切面自动记录变更日志
- **前端**：Vue3 + Element Plus实现日志查看界面
- **数据库**：MySQL存储变更日志数据
- **API**：RESTful API提供日志查询接口
